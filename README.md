<a id="readme-top"></a>

<!-- PROJECT LOGO -->
<br />
<div align="center">

<h3 align="center">User-Profile-Serving</h3>

  <p align="center">
    A project to serve user profile data for IBU services.
    <br />
    <br />
    <a href="https://git.dev.sh.ctripcorp.com/ibu-llm-user-profile/user-profile-serving/issues/new?labels=bug&template=bug-report---.md">Report Bug</a>
    &middot;
    <a href="https://git.dev.sh.ctripcorp.com/ibu-llm-user-profile/user-profile-serving/issues/new?labels=enhancement&template=feature-request---.md">Request Feature</a>
  </p>
</div>



<!-- TABLE OF CONTENTS -->
<details>
  <summary>Table of Contents</summary>
  <ol>
    <li>
      <a href="#about-the-project">About The Project</a>
      <ul>
        <li><a href="#built-with">Built With</a></li>
      </ul>
    </li>
    <li>
      <a href="#getting-started">Getting Started</a>
      <ul>
        <li><a href="#prerequisites">Prerequisites</a></li>
        <li><a href="#installation">Installation</a></li>
      </ul>
    </li>
    <li><a href="#usage">Usage</a></li>
    <li><a href="#roadmap">Roadmap</a></li>
    <li><a href="#contributing">Contributing</a></li>
    <li><a href="#license">License</a></li>
    <li><a href="#contact">Contact</a></li>
    <li><a href="#acknowledgments">Acknowledgments</a></li>
  </ol>
</details>



<!-- ABOUT THE PROJECT -->
## About The Project

<img src="./user-profile-soa/src/main/resources/images/IBU%20User%20Profile%20MVP.png">

This project provides a User Profile query service that processes user tracking data through cleansing and transformation to output L2-level User Profile data and L3-level scenario-specific data.

The process includes the following steps:

* Consume tracking data from specific topics, process it through cleansing and transformation to generate L2-level User Profile data, stored in OBKV. This is primarily accomplished through Flink Job tasks.
* Query User Profile data from OBKV based on user ID, process it through SummaryAgent to generate L3-level scenario-specific data, and return both L2 and L3 data to business services.
* Upon receiving L2 and L3 data, business services assemble appropriate business data according to their specific requirements and deliver it to the frontend.



### Built With
* [![Spring][Spring]][Spring-url]
* [![Spring Boot][Spring-boot]][Spring-boot-url]
* [![Maven][maven-shield]][maven-url]
* [![Java][java-shield]][java-url]


<p align="right">(<a href="#readme-top">back to top</a>)</p>



<!-- GETTING STARTED -->
## Getting Started
Before you start calling, you need to introduce the following dependencies

<br/>

```xml
<dependency>
    <groupId>com.ctrip.ibu.user-profile-serving</groupId>
    <artifactId>user-profile-serving</artifactId>
    <version>${project.version}</version>
</dependency>
```


<!-- USAGE EXAMPLES -->
## Usage
```java
import com.ctrip.ibu.userprofileserving.UserProfileServingClient;

```

<!-- ROADMAP -->
## Roadmap

- [ ] Open the connection channel with DBKV and add the ability to access DBKV data.
- [ ] Add interaction with SummaryAgent to Add the ability to obtain L3 level data.


<!-- CONTRIBUTING -->
## Contributing
If you have a suggestion that would make this better, please fork the repo and create a pull request. You can also simply open an issue with the tag "enhancement".
Don't forget to give the project a star! Thanks again!

1. Fork the Project
2. Create your Feature Branch (`git checkout -b feature/AmazingFeature`)
3. Commit your Changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the Branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

### Top contributors:


<!-- CONTACT -->
## Contact

jianweidai- <EMAIL> <br/>

wyijun- <EMAIL>

<!-- ACKNOWLEDGMENTS -->
## Acknowledgments

* [User Profile Record](http://conf.ctripcorp.com/pages/viewpage.action?pageId=3699452244)

<!-- MARKDOWN LINKS & IMAGES -->
<!-- https://www.markdownguide.org/basic-syntax/#reference-style-links -->
[Spring]: https://img.shields.io/badge/Spring-6DB33F?style=for-the-badge&logo=spring&logoColor=white
[Spring-url]: https://spring.io/
[Spring-boot]: https://img.shields.io/badge/Spring%20Boot-6DB33F?style=for-the-badge&logo=spring-boot&logoColor=white
[Spring-boot-url]: https://spring.io/projects/spring-boot
[maven-shield]: https://img.shields.io/badge/Maven-CC3D00?style=for-the-badge&logo=apache-maven&logoColor=white
[maven-url]: https://maven.apache.org/
[java-shield]: https://img.shields.io/badge/Java-ED8B00?style=for-the-badge&logo=java&logoColor=white
[java-url]: https://www.java.com