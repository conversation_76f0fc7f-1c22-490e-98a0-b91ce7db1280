<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.ctrip</groupId>
        <artifactId>super-pom</artifactId>
        <version>1.0.8-Java21</version>
    </parent>
    <groupId>com.ctrip.ibu</groupId>
    <artifactId>user-profile-serving</artifactId>
    <version>0.0.1</version>
    <name>user-profile-serving</name>
    <modules>
        <module>user-profile-application</module>
        <module>user-profile-service</module>
        <module>user-profile-soa</module>
    </modules>
    <packaging>pom</packaging>
    <description>100053425</description>
    <properties>
        <framework-bom.version>8.33.1-Java21</framework-bom.version>
        <java.version>21</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <releases.repo>http://maven.release.ctripcorp.com/nexus/content/repositories/iburelease</releases.repo>
        <snapshots.repo>http://maven.release.ctripcorp.com/nexus/content/repositories/ibusnapshot</snapshots.repo>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <obkv.version>3.0.8</obkv.version>
        <kotlin.version>2.1.20</kotlin.version>
        <kotlin.coroutines.version>1.10.2</kotlin.coroutines.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.ctrip.framework</groupId>
                <artifactId>framework-bom</artifactId>
                <version>${framework-bom.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>user-profile-service</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>user-profile-application</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-stdlib-jdk8</artifactId>
                <version>${kotlin.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-test</artifactId>
                <version>${kotlin.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-reflect</artifactId>
                <version>${kotlin.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>1.2.83</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.infosec.kms</groupId>
                <artifactId>kms-sdk</artifactId>
                <version>1.2.2-Java21</version>
            </dependency>
            <dependency>
                <groupId>com.google.genai</groupId>
                <artifactId>google-genai</artifactId>
                <version>1.1.0</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.ibu</groupId>
                <artifactId>user-profile-serving</artifactId>
                <version>1.0.2</version>
            </dependency>

            <dependency>
                <groupId>org.jetbrains.kotlinx</groupId>
                <artifactId>kotlinx-coroutines-core</artifactId>
                <version>${kotlin.coroutines.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jetbrains.kotlinx</groupId>
                <artifactId>kotlinx-coroutines-reactor</artifactId>
                <version>${kotlin.coroutines.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jetbrains.kotlinx</groupId>
                <artifactId>kotlinx-coroutines-slf4j</artifactId>
                <version>${kotlin.coroutines.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jsoup</groupId>
                <artifactId>jsoup</artifactId>
                <version>1.17.2</version>
            </dependency>

            <dependency>
                <groupId>com.ctrip.soa.hotel.wireless</groupId>
                <artifactId>hotelfronthotellistservice-contract</artifactId>
                <version>1.7.35</version>
            </dependency>

            <dependency>
                <groupId>com.ctrip.soa.hotel.wireless</groupId>
                <artifactId>hotelfrontfilteritemservice-contract</artifactId>
                <version>1.1.4</version>
                <exclusions>
                    <exclusion>
                        <artifactId>hotelfrontcommontype</artifactId>
                        <groupId>com.ctrip.hotel.wireless</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.ctrip.hotel.wireless</groupId>
                <artifactId>hotelfrontcommontype</artifactId>
                <version>1.1.2</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.soa.22244</groupId>
                <artifactId>cdpdataservice</artifactId>
                <version>0.0.102</version>
            </dependency>

            <!--Ktorm-->
            <dependency>
                <groupId>org.ktorm</groupId>
                <artifactId>ktorm-support-mysql</artifactId>
                <version>4.1.1</version>
            </dependency>
            <dependency>
                <groupId>org.ktorm</groupId>
                <artifactId>ktorm-jackson</artifactId>
                <version>4.1.1</version>
            </dependency>
            <dependency>
                <groupId>org.ktorm</groupId>
                <artifactId>ktorm-core</artifactId>
                <version>4.1.1</version>
            </dependency>

            <!--EasyExcel-->
            <dependency>
                <groupId>cn.afterturn</groupId>
                <artifactId>easypoi-spring-boot-starter</artifactId>
                <version>4.4.0</version>
            </dependency>

            <dependency>
                <groupId>com.ctrip.ibu.soa.26974</groupId>
                <artifactId>ibumytriporderservice</artifactId>
                <version>1.0.39-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.market</groupId>
                <artifactId>promocode-client</artifactId>
                <version>1.2.0</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <argLine>
                        @{argLine}
                        -Djdk.attach.allowAttachSelf=true
                        -XX:+EnableDynamicAgentLoading
                        --add-opens java.management/sun.management=ALL-UNNAMED
                        --add-opens java.base/java.lang=ALL-UNNAMED
                        --add-opens java.base/java.lang.reflect=ALL-UNNAMED
                        --add-opens java.base/sun.reflect.annotation=ALL-UNNAMED
                        --add-opens java.base/java.math=ALL-UNNAMED
                        --add-opens java.base/java.util=ALL-UNNAMED
                        --add-opens java.base/sun.util.calendar=ALL-UNNAMED
                        --add-opens java.base/java.io=ALL-UNNAMED
                        --add-opens java.base/java.net=ALL-UNNAMED
                        --add-opens java.xml/com.sun.org.apache.xerces.internal.jaxp.datatype=ALL-UNNAMED
                    </argLine>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-maven-plugin</artifactId>
                <version>${kotlin.version}</version>
                <executions>
                    <execution>
                        <id>compile</id>
                        <phase>compile</phase>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                        <configuration>
                            <sourceDirs>
                                <source>src/main/java</source>
                                <source>src/main/kotlin</source>
                                <source>target/generated-sources/annotations</source>
                            </sourceDirs>
                        </configuration>
                    </execution>
                    <execution>
                        <id>test-compile</id>
                        <phase>test-compile</phase>
                        <goals>
                            <goal>test-compile</goal>
                        </goals>
                        <configuration>
                            <sourceDirs>
                                <source>src/test/java</source>
                                <source>src/test/kotlin</source>
                                <source>target/generated-test-sources/test-annotations</source>
                            </sourceDirs>
                        </configuration>
                    </execution>
                </executions>
                <configuration>
                    <jvmTarget>${maven.compiler.target}</jvmTarget>
                    <args>
                        <arg>-Xjsr305=strict</arg>
                    </args>
                    <compilerPlugins>
                        <plugin>spring</plugin>
                        <plugin>jpa</plugin>
                        <plugin>no-arg</plugin>
                    </compilerPlugins>
                    <pluginOptions>
                        <option>no-arg:annotation=com.your.NoArg</option>
                    </pluginOptions>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>org.jetbrains.kotlin</groupId>
                        <artifactId>kotlin-maven-allopen</artifactId>
                        <version>${kotlin.version}</version>
                    </dependency>
                    <dependency>
                        <groupId>org.jetbrains.kotlin</groupId>
                        <artifactId>kotlin-maven-noarg</artifactId>
                        <version>${kotlin.version}</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <executions>
                    <execution>
                        <id>default-compile</id>
                        <phase>none</phase>
                    </execution>
                    <execution>
                        <id>default-testCompile</id>
                        <phase>none</phase>
                    </execution>
                    <execution>
                        <id>compile</id>
                        <phase>compile</phase>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>testCompile</id>
                        <phase>test-compile</phase>
                        <goals>
                            <goal>testCompile</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
