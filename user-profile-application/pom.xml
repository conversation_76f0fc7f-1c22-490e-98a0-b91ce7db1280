<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.ctrip.ibu</groupId>
        <artifactId>user-profile-serving</artifactId>
        <version>0.0.1</version>
    </parent>

    <artifactId>user-profile-application</artifactId>
    <packaging>jar</packaging>

    <name>user-profile-application</name>
    <url>http://maven.apache.org</url>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.ctrip.ibu</groupId>
            <artifactId>user-profile-service</artifactId>
        </dependency>

        <!-- Spring Framework -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
        </dependency>

        <!-- getHotelList-->
        <dependency>
            <groupId>com.ctrip.soa.hotel.wireless</groupId>
            <artifactId>hotelfronthotellistservice-contract</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>hotelfrontcommontype</artifactId>
                    <groupId>com.ctrip.hotel.wireless</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.ctrip.hotel.wireless</groupId>
            <artifactId>hotelfrontcommontype</artifactId>
        </dependency>

        <!-- Commons Lang -->
        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
            <version>2.6</version>
        </dependency>

        <dependency>
            <groupId>com.ctrip.ibu.cs</groupId>
            <artifactId>ibu-cs-llm-workbench-service-client</artifactId>
            <version>1.0.16</version>
        </dependency>

        <dependency>
            <groupId>com.ctrip.framework</groupId>
            <artifactId>fx-spring-boot</artifactId>
        </dependency>

        <!-- Logging -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ctrip.soa.hotel.wireless</groupId>
            <artifactId>hotelfrontfilteritemservice-contract</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>hotelfrontcommontype</artifactId>
                    <groupId>com.ctrip.hotel.wireless</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.ctrip.soa.22244</groupId>
            <artifactId>cdpdataservice</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ctrip.ibu.soa.26974</groupId>
            <artifactId>ibumytriporderservice</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.market</groupId>
            <artifactId>promocode-client</artifactId>
        </dependency>
    </dependencies>
</project>
