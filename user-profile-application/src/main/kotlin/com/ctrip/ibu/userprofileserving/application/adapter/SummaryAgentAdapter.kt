package com.ctrip.ibu.userprofileserving.application.adapter

import com.ctrip.ibu.userprofileserving.service.agent.SummaryAgent
import com.ctrip.ibu.userprofileserving.service.vo.DetailL2VO
import com.ctrip.ibu.userprofileserving.service.vo.SummaryL3VO
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

/**
 * Adapter class to interact with the SummaryAgent from the service layer
 * This allows the application layer to use the SummaryAgent functionality without direct dependencies
 */
@Component
class SummaryAgentAdapter(private val summaryAgent: SummaryAgent) {
    
    private val logger = LoggerFactory.getLogger(SummaryAgentAdapter::class.java)
    
    /**
     * Get user profile summary for a given time range
     */
    fun getUserProfileSummary(
        uid: String,
        startTime: String,
        endTime: String,
        topicId: String
    ): Pair<List<SummaryL3VO>, List<DetailL2VO>> {
        logger.info("Getting user profile summary for uid: $uid, timeRange: $startTime to $endTime")
        
        return try {
            summaryAgent.getSummary(uid, startTime, endTime, topicId)
        } catch (e: Exception) {
            logger.error("Error getting user profile summary: ${e.message}", e)
            Pair(emptyList(), emptyList())
        }
    }
    
    /**
     * Generate prompt and get LLM response for structured filter generation
     * This function will pass the user summary to an LLM to generate structured filter recommendations
     */
    fun generateFilterRecommendations(userSummary: String, promptTemplate: String): String {
        logger.info("Generating filter recommendations based on user summary")
        
        // Here we would normally call the LLM through the SummaryAgent
        // For now, we'll return a placeholder response
        
        // TODO: Replace with actual call to SummaryAgent's AIClient
        // return summaryAgent.getAIClient().generateMessage(listOf(mapOf("user" to promptTemplate)))
        
        // Placeholder response - in production this would come from the LLM
        return """
            {
              "filters": [
                {"filterId": "29|4", "type": "29", "value": "4|5", "subType": "2"},              ]
            }
        """.trimIndent()
    }
} 