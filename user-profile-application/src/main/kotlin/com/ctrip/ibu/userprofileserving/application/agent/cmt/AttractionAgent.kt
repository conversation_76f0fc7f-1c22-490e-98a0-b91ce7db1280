package com.ctrip.ibu.userprofileserving.application.agent.cmt

import com.alibaba.fastjson.JSON
import com.ctrip.ibu.userprofileserving.service.config.CommonConfig
import com.ctrip.ibu.userprofileserving.service.dao.entity.obkv.UserProfileTimeCost
import com.ctrip.ibu.userprofileserving.service.dto.AttractionResult
import com.ctrip.ibu.userprofileserving.service.dto.AttractionResultDTO
import com.ctrip.ibu.userprofileserving.service.dto.CMTRecReqDTO
import com.ctrip.ibu.userprofileserving.service.dto.CityData
import com.ctrip.ibu.userprofileserving.service.dto.DailyData
import com.ctrip.ibu.userprofileserving.service.dto.HotelFilterDTO
import com.ctrip.ibu.userprofileserving.service.service.UserProfileCostTimeService
import com.ctrip.ibu.userprofileserving.service.util.Constant
import com.ctrip.ibu.userprofileserving.service.util.JsonUtil
import com.ctrip.ibu.userprofileserving.service.util.OKHttpUtils
import io.dropwizard.metrics5.Counter
import io.dropwizard.metrics5.MetricName
import io.dropwizard.metrics5.MetricRegistry
import jakarta.annotation.PostConstruct
import org.springframework.stereotype.Component
import qunar.tc.qconfig.client.spring.QMapConfig
import kotlinx.coroutines.delay

@Component
class AttractionAgent(
    private val userProfileCostTimeService: UserProfileCostTimeService,
    private val metric: MetricRegistry
) {

    @QMapConfig(value = "common.properties")
    lateinit var commonConfig: CommonConfig

    companion object {
        private val logger = org.slf4j.LoggerFactory.getLogger(AttractionAgent::class.java)
        private const val MAX_RETRIES = 30
        private const val RETRY_DELAY_MS = 1000L
    }

    private var successCounter: Counter? = null

    private var failCounter: Counter? = null

    @PostConstruct
    fun init() {
        // add metric tags
        val tags = mutableMapOf<String, String>()
        tags["appid"] = Constant.APPID

        successCounter = metric.counter(MetricName(Constant.CMT_REC_ATTRACTION_METRIC_SUCCESS, tags))
        failCounter = metric.counter(MetricName(Constant.CMT_REC_ATTRACTION_METRIC_FAIL, tags))
    }

    /**
     * Generate attraction description based on itinerary information
     * @param req The CMT request data
     * @param description The itinerary information
     * @return Attraction result or null if generation failed
     */
    suspend fun getAttractionData(req: CMTRecReqDTO, description: String, requestId: String): AttractionResult? {
        val startTime = System.currentTimeMillis()
        val uid = req.uid
        logger.info("Generating attraction data for description: $description,requestId: $requestId, uid: $uid")

        var input: String? = null
        var output: String? = null
        try {
            // Submit request
            val (submitResult, requestBodyJson) = submit(requestId, req, description)
            logger.info("Submit result: $submitResult, Request body: $requestBodyJson, requestId: $requestId")

            input = requestBodyJson
            if (submitResult == Constant.CMT_ATTRACTION_STATUS_SUCCESS) {
                // Check request status
                val statusResult = getStatus(requestId)
                logger.info("Status result: $statusResult, requestId: $requestId")

                // Get result if completed
                return if (statusResult == Constant.CMT_ATTRACTION_COMPLETED) {
                    val (attractionResult, httpResponse) = getResult(requestId)
                    output = httpResponse
                    logger.info("HTTP response: $httpResponse")
                    successCounter?.inc()
                    attractionResult
                } else null
            } else return null
        } finally {
            val costTime = System.currentTimeMillis() - startTime
            logger.info("Attraction data generation for uid $uid completed in $costTime ms,requestId: $requestId")

            userProfileCostTimeService.saveCostTime(
                UserProfileTimeCost(
                    uid = uid,
                    requestId = requestId,
                    costType = Constant.ATTRACTION_COST_TYPE,
                    costTime = costTime,
                    input = input,
                    output = output
                )
            )
        }
    }

    /**
     * Submit attraction request
     * @param requestId Unique request identifier
     * @param req The CMT request data
     * @param description The itinerary information
     * @return Pair containing status code from the response and request body JSON string, (-1, "") if request failed
     */
    private fun submit(requestId: String, req: CMTRecReqDTO, description: String): Pair<Int, String> {
        return try {
            val requestBody = mapOf(
                "request_id" to requestId,
                "cmt_request" to req,
                "schedule_describe" to description
            )
            val requestBodyJson = JsonUtil.toJson(requestBody) ?: ""

            val result = OKHttpUtils.postSafe(
                url = commonConfig.attractionSlb + Constant.CMT_ATTRACTION_PATH,
                headers = mapOf(
                    "Content-Type" to "application/json",
                    "Accept" to "application/json"
                ),
                body = requestBodyJson
            )

            val statusCode = result.fold(
                onSuccess = { response ->
                    try {
                        JSON.parseObject(response)
                            .getJSONObject("status")
                            ?.getIntValue("code") ?: -1
                    } catch (e: Exception) {
                        logger.error("Failed to parse submit response JSON: ${e.message},requestId:$requestId", e)
                        -1
                    }
                },
                onFailure = { exception ->
                    logger.error("Submit request failed: ${exception.message},requestId:$requestId", exception)
                    failCounter?.inc()
                    -1
                }
            )

            Pair(statusCode, requestBodyJson)
        } catch (e: Exception) {
            logger.error("Unexpected error in submit: ${e.message},requestId:$requestId", e)
            failCounter?.inc()
            Pair(-1, "")
        }
    }

    /**
     * Check status of attraction request
     * @param requestId Unique request identifier
     * @return Request status string, "error" if request failed
     */
    private suspend fun getStatus(requestId: String): String {
        repeat(MAX_RETRIES) { attempt ->
            try {
                val result = OKHttpUtils.postSafe(
                    url = commonConfig.attractionSlb + Constant.CMT_ATTRACTION_STATUS_PATH + requestId,
                    headers = mapOf(
                        "Content-Type" to "application/json",
                        "Accept" to "application/json"
                    ),
                    body = null
                )

                val statusResult = result.fold(
                    onSuccess = { response -> response },
                    onFailure = { exception ->
                        logger.error(
                            "Status request failed (attempt ${attempt + 1}/$MAX_RETRIES): ${exception.message},requestId:$requestId",
                            exception
                        )
                        if (attempt == MAX_RETRIES - 1) {
                            failCounter?.inc()
                            return "error"
                        } else {
                            delay(RETRY_DELAY_MS)
                            return@repeat
                        }
                    }
                )

                val (statusCode, requestStatus) = try {
                    val jsonResult = JSON.parseObject(statusResult)
                    val code = jsonResult.getJSONObject("status")?.getIntValue("code") ?: -1
                    val status = jsonResult.getString("request_status") ?: ""
                    Pair(code, status)
                } catch (e: Exception) {
                    logger.error(
                        "Failed to parse status response JSON (attempt ${attempt + 1}/$MAX_RETRIES): ${e.message},requestId:$requestId",
                        e
                    )
                    if (attempt == MAX_RETRIES - 1) {
                        failCounter?.inc()
                        return "error"
                    } else {
                        delay(RETRY_DELAY_MS)
                        return@repeat
                    }
                }

                // If completed, return status directly
                if (statusCode == 0 && requestStatus == "completed") {
                    return requestStatus
                }

                // If still processing and not reached max retries, delay and continue to next iteration
                if ((requestStatus == "received" || requestStatus == "processing") && statusCode == 0) {
                    logger.info("Request status is $requestStatus, retrying (attempt ${attempt + 1}/$MAX_RETRIES)")
                    if (attempt < MAX_RETRIES - 1) {
                        delay(RETRY_DELAY_MS)
                    } else {
                        // Reached max retries, return current status
                        return requestStatus
                    }
                } else {
                    // Other cases, return current status directly
                    return requestStatus
                }
            } catch (e: Exception) {
                logger.error(
                    "Unexpected error in getStatus (attempt ${attempt + 1}/$MAX_RETRIES): ${e.message},requestId:$requestId",
                    e
                )
                if (attempt == MAX_RETRIES - 1) {
                    failCounter?.inc()
                    return "error"
                } else {
                    delay(RETRY_DELAY_MS)
                }
            }
        }

        return "retry_timeout"
    }

    /**
     * Get attraction result data
     * @param requestId Unique request identifier
     * @return Pair containing processed attraction result and original HTTP response string
     */
    private fun getResult(requestId: String): Pair<AttractionResult, String> {
        try {
            logger.info("Getting attraction result for requestId: $requestId")

            val requestBody = mapOf("request_id" to requestId)

            // Serialize request body to JSON
            val requestBodyJson = try {
                JsonUtil.toJson(requestBody)
            } catch (e: Exception) {
                logger.error("Failed to serialize request body to JSON: ${e.message},requestId:$requestId", e)
                throw IllegalStateException("Failed to serialize request body", e)
            }

            logger.info("Request body: $requestBodyJson")

            // Make HTTP request
            val result = OKHttpUtils.getSafe(
                url = commonConfig.attractionSlb + Constant.CMT_ATTRACTION_ALL_PATH,
                queryParams = mapOf("request_id" to requestId),
            ).fold(
                onSuccess = { response -> response },
                onFailure = { exception ->
                    logger.error(
                        "HTTP request failed for requestId $requestId: ${exception.message},requestId:$requestId",
                        exception
                    )
                    failCounter?.inc()
                    return Pair(AttractionResult(cities = emptyMap()), "")
                }
            )

            // Validate response
            if (result.isBlank()) {
                logger.error("Received empty response for requestId: $requestId")
                return Pair(AttractionResult(cities = emptyMap()), result)
            }

            logger.info("Received response: $result")

            // Parse JSON response
            val attractionResultDTO = try {
                JsonUtil.fromJsonResult(result, AttractionResultDTO::class.java).getOrThrow()
            } catch (e: Exception) {
                logger.error("Failed to parse JSON response for requestId $requestId: ${e.message}", e)
                logger.debug("Full response content: $result")
                return Pair(AttractionResult(cities = emptyMap()), result)
            }

            // Validate parsed DTO
            if (attractionResultDTO == null) {
                logger.error("Parsed AttractionResultDTO is null for requestId: $requestId")
                return Pair(AttractionResult(cities = emptyMap()), result)
            }

            logger.info("Successfully parsed attraction result with ${attractionResultDTO.attractions.size} attractions and ${attractionResultDTO.hotelFilters.size} hotel filters,requestId:$requestId")

            // Process the result
            val processedResult = try {
                processAttractionResult(attractionResultDTO)
            } catch (e: Exception) {
                logger.error("Failed to process attraction result for requestId $requestId: ${e.message}", e)
                AttractionResult(cities = emptyMap())
            }

            return Pair(processedResult, result)

        } catch (e: Exception) {
            logger.error("Unexpected error getting attraction result for requestId $requestId: ${e.message}", e)
            return Pair(AttractionResult(cities = emptyMap()), "")
        }
    }

    /**
     * Process attraction result data into the final format
     * @param attractionResultDTO Raw attraction result data
     * @return Processed attraction result
     */
    private fun processAttractionResult(attractionResultDTO: AttractionResultDTO): AttractionResult {
        return try {
            logger.info("Processing attraction result with ${attractionResultDTO.attractions.size} attractions and ${attractionResultDTO.hotelFilters.size} hotel filters")

            val cities = mutableMapOf<Int, CityData>()

            // Create hotel filter lookup map for efficient access
            val hotelFilterMap = attractionResultDTO.hotelFilters
                .flatMap { hotelFilter ->
                    hotelFilter.hotelLocationFilters.map { locationFilter ->
                        Triple(hotelFilter.cityId, locationFilter.date, locationFilter)
                    }
                }
                .groupBy({ it.first to it.second }, { it.third })

            val transportationMap = attractionResultDTO.cityTransportation
                .flatMap { cityTransportation ->
                    cityTransportation.dailyTransportation.map { transportation ->
                        Pair(cityTransportation.cityId to transportation.date, transportation)
                    }
                }
                .toMap()

            // Process attractions and merge hotel filters in single iteration
            attractionResultDTO.attractions.forEach { attraction ->
                val dailyDataMap = attraction.dailyAttractions.associate { dailyAttraction ->
                    val hotelLocationFilter = hotelFilterMap[attraction.cityId to dailyAttraction.date]?.firstOrNull()
                    val hotelFilterDTO = hotelLocationFilter?.let {
                        HotelFilterDTO(
                            title = it.title,
                            value = it.value,
                            filterId = it.filterId,
                            type = it.type,
                            subType = null,
                            priority = null
                        )
                    }
                    val transportationTip =
                        transportationMap[attraction.cityId to dailyAttraction.date]?.transportAdvice ?: ""

                    dailyAttraction.date to DailyData(
                        attractionIds = dailyAttraction.attractions.map { it.id },
                        attractionNames = dailyAttraction.attractions.map { it.name },
                        recommendText = dailyAttraction.summary,
                        hotelFilterDTO = hotelFilterDTO,
                        transportation = transportationTip
                    )
                }

                cities[attraction.cityId] = CityData(
                    cityName = attraction.cityName,
                    dailyData = dailyDataMap
                )
            }

            logger.info("Successfully processed attraction result with ${cities.size} cities")
            AttractionResult(cities = cities)

        } catch (e: Exception) {
            logger.error("Error processing attraction result: ${e.message}", e)
            AttractionResult(cities = emptyMap())
        }
    }
}