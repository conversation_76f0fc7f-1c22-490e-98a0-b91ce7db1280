package com.ctrip.ibu.userprofileserving.application.agent.cmt

import com.ctrip.ibu.userprofileserving.application.dto.CMTHotelFilterRequest
import com.ctrip.ibu.userprofileserving.application.service.CMTHotelService
import com.ctrip.ibu.userprofileserving.service.agent.AIClient
import com.ctrip.ibu.userprofileserving.service.config.UserPromptParse
import com.ctrip.ibu.userprofileserving.service.dao.entity.obkv.UserProfileTimeCost
import com.ctrip.ibu.userprofileserving.service.dto.CMTRecReqDTO
import com.ctrip.ibu.userprofileserving.service.dto.HotelFilterDTO
import com.ctrip.ibu.userprofileserving.service.dto.HotelFilterUnit
import com.ctrip.ibu.userprofileserving.service.dto.Parameter
import com.ctrip.ibu.userprofileserving.service.dto.PrdTypeRecDTO
import com.ctrip.ibu.userprofileserving.service.enums.CMTProductType
import com.ctrip.ibu.userprofileserving.service.service.UserProfileCostTimeService
import com.ctrip.ibu.userprofileserving.service.util.CommonUtil
import com.ctrip.ibu.userprofileserving.service.util.Constant
import com.ctrip.ibu.userprofileserving.service.util.JsonUtil
import io.dropwizard.metrics5.Counter
import io.dropwizard.metrics5.MetricName
import io.dropwizard.metrics5.MetricRegistry
import jakarta.annotation.PostConstruct
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import qunar.tc.qconfig.client.TypedConfig

@Component
class HotelFilterAgent(
    private val cmtHotelService: CMTHotelService,
    private val aiClient: AIClient,
    private val userProfileCostTimeService: UserProfileCostTimeService,
    private val metric: MetricRegistry
) {

    companion object {
        private val logger = LoggerFactory.getLogger(HotelFilterAgent::class.java)
    }

    private var successCounter: Counter? = null

    private var failCounter: Counter? = null

    @PostConstruct
    fun init(){

        // add metric tags
        val tags = mutableMapOf<String, String>()
        tags["appid"] = Constant.APPID

        // Initialize counters for success and failure metrics
        successCounter = metric.counter(MetricName(Constant.CMT_REC_HOTEL_FILTER_METRIC_SUCCESS, tags))
        failCounter = metric.counter(MetricName(Constant.CMT_REC_HOTEL_FILTER_METRIC_FAIL, tags))
    }

    /**
     * Generate hotel filter data based on L3 result and description
     * @param l3Result User profile L3 data
     * @param productSelData Product selection data
     * @param requestId Request ID for logging and cost tracking
     * @param uid User ID for cost tracking
     * @return Map of day indices to hotel parameters with filters
     */
    suspend fun getHotelFilterNoPOIData(
        l3Result: String,
        productSelData: List<PrdTypeRecDTO>,
        requestId: String,
        req: CMTRecReqDTO
    ): Map<Int, Parameter> {
        val startTime = System.currentTimeMillis()
        logger.info(
            "Generating hotel filter data for productSelData: ${JsonUtil.toJson(productSelData)} " +
            "and l3Result: $l3Result,requestId: $requestId, uid: ${req.context.uid}"
        )

        try {
            // Collect all hotel recommendation tasks
            val hotelRecommendations = productSelData.flatMapIndexed { dayIndex, dayData ->
                dayData.dailySchedule.mapIndexedNotNull { productIndex, product ->
                    if (product.productType.lowercase() == CMTProductType.HOTEL.value.lowercase() &&
                        product.status.lowercase() == Constant.CMT_RECOMMENDATION_STATUS) {
                        dayIndex to product.parameters
                    } else null
                }
            }.toMap()

            return generateHotelFilters(hotelRecommendations, l3Result, req.uid, requestId, req)
        } finally {
            val costTime = System.currentTimeMillis() - startTime
            logger.info("Hotel filter generation for uid ${req.context.uid} completed in $costTime ms,requestId: $requestId")

            userProfileCostTimeService.saveCostTime(UserProfileTimeCost(
                uid = req.context.uid,
                requestId = requestId,
                costType = Constant.HOTEL_FILTER_COST_TYPE,
                costTime = costTime,
                input = null,
                output = null
            ))
        }
    }

    /**
     * Generates hotel filters for multiple hotel recommendation tasks using the first task's result.
     *
     * This method executes hotel filter generation only for the first task, then applies
     * the same hotel filters to all other tasks. This optimizes performance by avoiding
     * redundant LLM calls while maintaining consistent filter results across all tasks.
     */
    private suspend fun generateHotelFilters(
        hotelRecommendations: Map<Int, Parameter>,
        l3Result: String,
        uid: String,
        requestId: String,
        req: CMTRecReqDTO
    ): Map<Int, Parameter> {
        if (hotelRecommendations.isEmpty()) {
            logger.info("No hotel recommendations to process")
            return emptyMap()
        }

        logger.info("Starting hotel filter generation for ${hotelRecommendations.size} tasks using first task result,requestId: $requestId, uid: $uid")

        // Get the first task
        val firstEntry = hotelRecommendations.entries.first()
        val firstDayIndex = firstEntry.key
        val firstParams = firstEntry.value

        val taskStartTime = System.currentTimeMillis()
        var input: String? = null
        var output: String? = null
        var sharedHotelFilters: List<HotelFilterDTO>

        try {
            // Get hotel filters and build prompt once for all subsequent operations
            val filters = getHotelFilters(
                checkin = firstParams.checkin,
                checkout = firstParams.checkout,
                cityId = firstParams.cityId,
                req,
                requestId = requestId
            )

            // Build complete prompt once - this ensures input is available even if LLM call fails
            val promptsJson = buildHotelFilterPrompt(firstParams, l3Result, filters, req)

            input = promptsJson

            // Execute hotel filter generation with pre-built prompt
            val (hotelFilters, result) = recommendHotelFiltersL3(promptsJson, filters, req, requestId)
            output = result
            sharedHotelFilters = hotelFilters

            logger.info("Successfully generated hotel filters from first task, applying to all ${hotelRecommendations.size} tasks, requestId: $requestId, uid: $uid")
        } catch (e: Exception) {
            logger.error(
                "Error generating hotel filters for first task day=$firstDayIndex: ${e.message} requestId:$requestId,uid:$uid",
                e
            )
            failCounter?.inc()
            // If first task fails, return all original parameters without hotel filters
            return hotelRecommendations
        } finally {
            val taskCostTime = System.currentTimeMillis() - taskStartTime
            try {
                userProfileCostTimeService.saveCostTime(
                    UserProfileTimeCost(
                        uid = uid,
                        requestId = requestId,
                        costType = Constant.HOTEL_FILTER_COST_TYPE + "_$firstDayIndex",
                        costTime = taskCostTime,
                        input = input,
                        output = output
                    )
                )
            } catch (e: Exception) {
                logger.warn("Failed to save cost time for first task day=$firstDayIndex: ${e.message}")
            }
        }

        // Apply the shared hotel filters to all tasks
        val results = hotelRecommendations.mapValues { (_, params) ->
            params.copy(hotelFilters = sharedHotelFilters)
        }

        logger.info("Hotel filter generation completed for ${results.size} tasks using shared filters,requestId: $requestId, uid: $uid")
        return results
    }

    /**
     * Generate recommended hotel filters based on LLM, using pre-built prompt
     */
    private suspend fun recommendHotelFiltersL3(
        promptsJson: String,
        filters: List<HotelFilterUnit>,
        req: CMTRecReqDTO,
        requestId: String
    ): Pair<List<HotelFilterDTO>, String> {
        logger.info("recommendHotelFiltersL3 prompt: $promptsJson, requestId: $requestId, uid: ${req.context.uid}")
        val prompts = listOf(mapOf("user" to promptsJson))

        // Call LLM to generate response
        val result = aiClient.generateResponse(prompts, Constant.VERTEX)

        // Parse JSON result with exception handling
        val hotelFilters = try {
            val jsonText = extractJsonFromLLMResponse(result)
            logger.info("Hotel filter JSON: $jsonText ,requestId: $requestId, uid: ${req.context.uid}")

            // Parse JSON array with proper exception handling using JsonUtil
            JsonUtil.fromJsonArrayResult(jsonText, HotelFilterDTO::class.java)
                .fold(
                    onSuccess = { result ->
                        val hotelFilters = result ?: emptyList()
                        logger.info("Successfully parsed ${hotelFilters.size} hotel filters, requestId: $requestId, uid: ${req.context.uid}")
                        hotelFilters
                    },
                    onFailure = { exception ->
                        logger.error("Failed to parse JSON array to HotelFilterDTO list: ${exception.message}, requestId:$requestId", exception)
                        failCounter?.inc()
                        emptyList()
                    }
                )

        } catch (e: Exception) {
            logger.error("Failed to extract JSON from LLM response: ${e.message},requestId:$requestId", e)
            failCounter?.inc()
            emptyList()
        }

        // Enrich hotel filters with complete information from original filters
        val enrichedHotelFilters = enrichHotelFiltersWithCompleteInfo(hotelFilters, filters, requestId, req.context.uid)
        successCounter?.inc()

        return Pair(enrichedHotelFilters, result)
    }

    private fun extractJsonFromLLMResponse(response: String): String {
        try {
            logger.debug("Extracting JSON from LLM response: ${response.take(200)}...")

            // Validate input
            if (response.isBlank()) {
                throw IllegalStateException("LLM response is empty or blank")
            }

            var extractedJson = ""

            // First try to extract from <hotel_filters> tags
            if (response.contains("<hotel_filters>") && response.contains("</hotel_filters>")) {
                logger.debug("Found hotel_filters tags, extracting content...")
                val hotelFiltersStart = response.indexOf("<hotel_filters>") + "<hotel_filters>".length
                val hotelFiltersEnd = response.indexOf("</hotel_filters>")

                if (hotelFiltersEnd > hotelFiltersStart) {
                    val hotelFiltersContent = response.substring(hotelFiltersStart, hotelFiltersEnd).trim()

                    // Check if there's JSON content within the hotel_filters tags
                    if (hotelFiltersContent.contains("```json")) {
                        extractedJson = extractJsonFromMarkdown(hotelFiltersContent)
                    } else {
                        // If no ```json markers, treat the whole content as JSON
                        extractedJson = hotelFiltersContent
                    }
                }
            }
            // Fallback to original logic for ```json markers
            else if (response.contains("```json")) {
                extractedJson = extractJsonFromMarkdown(response)
            }
            else {
                throw IllegalStateException("LLM response does not contain expected JSON markers (```json or <hotel_filters>)")
            }

            if (extractedJson.isBlank()) {
                throw IllegalStateException("Extracted JSON content is empty")
            }

            logger.debug("Successfully extracted JSON: ${extractedJson.take(100)}...")
            return extractedJson

        } catch (e: Exception) {
            logger.error("Failed to extract JSON from LLM response: ${e.message}", e)
            throw IllegalStateException("Failed to extract JSON from LLM response: ${e.message}", e)
        }
    }

    private fun extractJsonFromMarkdown(content: String): String {
        // Check if there's a closing ``` marker after the ```json marker
        val jsonMarkerIndex = content.indexOf("```json")
        val afterJsonMarkerIndex = jsonMarkerIndex + "```json".length
        if (content.indexOf("```", afterJsonMarkerIndex) == -1) {
            throw IllegalStateException("Content does not contain closing '```' marker")
        }

        // Extract JSON part - content between ```json and ```
        val jsonParts = try {
            content.split("```json")
        } catch (e: Exception) {
            throw IllegalStateException("Failed to split content by '```json': ${e.message}", e)
        }

        if (jsonParts.size < 2) {
            throw IllegalStateException("No content found after '```json' marker")
        }

        val afterJsonMarker = jsonParts[1]
        val closingParts = try {
            afterJsonMarker.split("```")
        } catch (e: Exception) {
            throw IllegalStateException("Failed to split content by closing '```': ${e.message}", e)
        }

        if (closingParts.isEmpty()) {
            throw IllegalStateException("No content found between JSON markers")
        }

        return closingParts[0].trim()
    }

    /**
     * Get hotel filter list
     */
    private fun getHotelFilters(
        checkin: String?,
        checkout: String?,
        cityId: Int?,
        req: CMTRecReqDTO,
        requestId: String
    ): List<HotelFilterUnit> {
        val filterRequest = CMTHotelFilterRequest(
            checkIn = checkin,
            checkOut = checkout,
            cityId = cityId,
            head = req.head,
            locale = req.context.locale,
            traceId = req.context.traceId,
            uid = req.context.uid,
            requestId = requestId
        )
        return cmtHotelService.getHotelFilters(filterRequest)
    }

    /**
     * Convert filter list to prompt format
     */
    private fun fillPromptFilters(filterItems: List<HotelFilterUnit>): String {
        val filteredDictFilters = filtersDropType(filterItems)

        val sb = StringBuilder()
        filteredDictFilters.forEach { (groupTitle, filters) ->
            sb.append("filter类型：$groupTitle\n")
            filters.forEach { filter ->
                // Only include filters that have a value (exclude category headers)
                if (!filter.value.isNullOrEmpty()) {
                    val filterDict = buildMap<String, String> {
                        put("filterId", filter.filterId)
                        put("type", filter.type)
                        put("title", filter.title ?: "")
                        put("value", filter.value ?: "")
                    }
                    sb.append(formatFilterDict(filterDict)).append("\n")
                }
            }
            sb.append("\n")
        }
        return sb.toString().trim()
    }

    /**
     * Format filter dictionary as string representation
     */
    private fun formatFilterDict(filterDict: Map<String, String>): String {
        val entries = filterDict.entries.joinToString(", ") { (key, value) ->
            "'$key': '$value'"
        }
        return "{$entries}"
    }

    /**
     * Filter and group filters by type, using category title as group key
     * @param filterItems List of filter items
     * @param dropType Filter types to exclude
     * @return Map of filters grouped by type with category title as key
     */
    private fun filtersDropType(
        filterItems: List<HotelFilterUnit>,
        dropType: List<String> = listOf("2", "17", "18")
    ): Map<String, List<HotelFilterUnit>> {
        val filteredItems = filterItems.filter { it.type !in dropType }

        // Group by type first
        val groupedByType = filteredItems.groupBy { it.type }

        // Convert to map with category title as key
        return groupedByType.mapKeys { (type, items) ->
            // Find the category header (item without value) for this type
            val categoryHeader = items.find { it.value.isNullOrEmpty() }
            categoryHeader?.title ?: type // Use category title or fallback to type
        }
    }

    /**
     * Enrich hotel filters with complete information from original filters
     * @param hotelFilters Parsed hotel filters from LLM response (may have incomplete info)
     * @param originalFilters Complete filter information from SOA service
     * @param requestId Request ID for logging
     * @param uid User ID for logging
     * @return List of HotelFilterDTO with complete information
     */
    private fun enrichHotelFiltersWithCompleteInfo(
        hotelFilters: List<HotelFilterDTO>,
        originalFilters: List<HotelFilterUnit>,
        requestId: String,
        uid: String
    ): List<HotelFilterDTO> {
        // Create a map for quick lookup of original filters by filterId
        val originalFilterMap = originalFilters.associateBy { it.filterId }

        val enrichedFilters = hotelFilters.map { hotelFilter ->
            val originalFilter = originalFilterMap[hotelFilter.filterId]
            if (originalFilter != null) {
                // Enrich with complete information from original filter
                HotelFilterDTO(
                    title = originalFilter.title ?: hotelFilter.title,
                    value = originalFilter.value ?: hotelFilter.value,
                    filterId = hotelFilter.filterId,
                    type = originalFilter.type, // Keep original type if exists
                    subType = hotelFilter.subType, // Keep original subType if exists
                    priority = hotelFilter.priority // Keep original priority if exists
                )
            } else {
                logger.warn("Original filter not found for filterId: ${hotelFilter.filterId}, requestId: $requestId, uid: $uid")
                // Return the original hotelFilter if no match found
                hotelFilter
            }
        }

        logger.info("Enriched ${enrichedFilters.size} hotel filters with complete information, requestId: $requestId, uid: $uid")
        return enrichedFilters
    }

    /**
     * Build hotel filter prompt with all necessary information.
     * This method contains the shared logic for preparing the prompt that will be sent to LLM.
     *
     * @param hotelParams Hotel parameters
     * @param listUbtL3 User profile L3 data
     * @param req CMT request
     * @param requestId Request ID for logging
     * @param filters Pre-fetched hotel filters to avoid duplicate SOA calls
     * @return Complete prompt string that will be sent to LLM
     */
    private suspend fun buildHotelFilterPrompt(
        hotelParams: Parameter,
        listUbtL3: String,
        filters: List<HotelFilterUnit>,
        req: CMTRecReqDTO
    ): String {
        // Build prompt with filter information
        val promptHotelFilters = fillPromptFilters(filters)

        // Get configured prompt template
        val promptProperties = TypedConfig.get("recommend-hotel-filter-prompt.yml", UserPromptParse())
        val properties = promptProperties.current()

        // Prepare future itinerary parameters
        val futureItinerary = buildMap {
            put("checkin", hotelParams.checkin ?: "")
            put("checkout", hotelParams.checkout ?: "")
            put("cityId", hotelParams.cityId ?: 0)
            put("cityName", hotelParams.cityName ?: "")
            put("adult", req.travelers.adultCount ?: 1)
            put("children", req.travelers.childCount ?: 0)
        }

        // Build complete prompt
        val sb = StringBuilder()
        sb.append(properties.user ?: "")
        sb.append("\n用户近期行为:${CommonUtil.cleanJsonForPrompt(listUbtL3)}")
        sb.append("\n未来行程:${CommonUtil.cleanJsonForPrompt(JsonUtil.toJson(futureItinerary))}")
        sb.append("\nfilter候选列表:\n$promptHotelFilters")

        return sb.toString()
    }


}