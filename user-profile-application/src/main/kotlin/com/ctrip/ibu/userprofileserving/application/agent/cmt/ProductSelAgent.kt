package com.ctrip.ibu.userprofileserving.application.agent.cmt

import com.ctrip.ibu.userprofileserving.service.agent.AIClient
import com.ctrip.ibu.userprofileserving.service.config.CommonConfig
import com.ctrip.ibu.userprofileserving.service.config.UserPromptParse
import com.ctrip.ibu.userprofileserving.service.dao.entity.obkv.UserProfileTimeCost
import com.ctrip.ibu.userprofileserving.service.dto.CMTRecReqDTO
import com.ctrip.ibu.userprofileserving.service.dto.PrdTypeRecDTO
import com.ctrip.ibu.userprofileserving.service.service.UserProfileCostTimeService
import com.ctrip.ibu.userprofileserving.service.util.Constant
import com.ctrip.ibu.userprofileserving.service.util.JsonUtil
import com.ctrip.ibu.userprofileserving.service.util.OKHttpUtils
import com.ctrip.ibu.userprofileserving.service.util.RetryUtils
import io.dropwizard.metrics5.Counter
import io.dropwizard.metrics5.MetricName
import io.dropwizard.metrics5.MetricRegistry
import jakarta.annotation.PostConstruct
import org.springframework.stereotype.Component
import qunar.tc.qconfig.client.TypedConfig
import qunar.tc.qconfig.client.spring.QMapConfig

@Component
class ProductSelAgent(
    private val aiClient: AIClient,
    private val userProfileCostTimeService: UserProfileCostTimeService,
    private val metric: MetricRegistry
) {

    companion object {
        private val logger = org.slf4j.LoggerFactory.getLogger(ProductSelAgent::class.java)
    }

    @QMapConfig(value = "common.properties")
    lateinit var commonConfig: CommonConfig

    private var successCounter: Counter? = null

    private var failCounter: Counter? = null

    @PostConstruct
    fun init() {
        // add metric tags
        val tags = mutableMapOf<String, String>()
        tags["appid"] = Constant.APPID

        // Initialize counters for success and failure metrics
        successCounter = metric.counter(MetricName(Constant.CMT_REC_PRODUCT_SEL_METRIC_SUCCESS, tags))
        failCounter = metric.counter(MetricName(Constant.CMT_REC_PRODUCT_SEL_METRIC_FAIL, tags))
    }


    /**
     * Get product selection data based on the description.
     */
    suspend fun getProductSelData(description: String, requestId: String, uid: String): List<PrdTypeRecDTO> {
        val startTime = System.currentTimeMillis()
        logger.info("Generating product selection data request:$requestId for description: $description")

        var input: String? = null
        var output: String? = null
        try {
            val promptProperties = TypedConfig.get("product-select-prompt.yml", UserPromptParse())
            val properties = promptProperties.current()

            // Combine user prompt with description
            val userPrompt = properties.user?.plus(description) ?: description
            val prompts = listOf(mapOf("user" to userPrompt))
            input = userPrompt

            logger.info("ProductSelAgent requestId:$requestId prompts: $prompts")

            // Generate response from AI client
            val prdTypeRecommend = aiClient.generateResponse(prompts, Constant.VERTEX)
            output = prdTypeRecommend

            // Extract schedule data from response
            return extractScheduleData(prdTypeRecommend)
        } finally {
            val costTime = System.currentTimeMillis() - startTime
            logger.info("Product selection generation requestId:$requestId for uid $uid completed in $costTime ms")

            userProfileCostTimeService.saveCostTime(UserProfileTimeCost(
                uid = uid,
                requestId = requestId,
                costType = Constant.PRODUCT_SEL_COST_TYPE,
                costTime = costTime,
                input = input,
                output = output
            ))
        }
    }

    suspend fun getProductSelDataByRequest(
        req: CMTRecReqDTO,
        requestId: String,
        description: String
    ): List<PrdTypeRecDTO> {
        val startTime = System.currentTimeMillis()
        var input: String? = null
        var output: String? = null

        try {
            val requestBody = mapOf(
                "request_id" to requestId,
                "cmt_request" to req,
                "schedule_describe" to description
            )
            val body = JsonUtil.toJson(requestBody)
            input = body
            logger.info("get product select for request body: $body, requestId: $requestId")

            // Use RetryUtils to handle timeout exceptions with retry mechanism
            val result = RetryUtils.executeWithRetryAndDefaultResponse(
                operation = "Product Select API Call (requestId: $requestId)",
                maxRetries = 3,
                initialDelayMs = 1000L,
                createDefaultResponse = { "" }
            ) {
                OKHttpUtils.post(
                    url = commonConfig.attractionSlb + Constant.CMT_PRODUCT_SEL_PATH,
                    headers = mapOf(
                        "Content-Type" to "application/json",
                        "Accept" to "application/json"
                    ),
                    body = body
                )
            }

            // add logging and failure counter
            if (result.isBlank()) {
                logger.error("Product select API returned empty response for requestId: $requestId")
                failCounter?.inc()
                return emptyList()
            }

            output = result

            logger.info("Product select API response for requestId: $requestId, response: ${result?.take(200)}...")

            // Convert string response to List<PrdTypeRecDTO>
            return convertStringToProductSelList(result, requestId)

        } catch (e: Exception) {
            logger.error("Error calling product select API for requestId: $requestId: ${e.message}", e)
            return emptyList()
        } finally {
            val costTime = System.currentTimeMillis() - startTime
            logger.info("Product select API call for requestId: $requestId completed in $costTime ms")

            userProfileCostTimeService.saveCostTime(UserProfileTimeCost(
                uid = req.uid,
                requestId = requestId,
                costType = Constant.PRODUCT_SEL_REQUEST_COST_TYPE,
                costTime = costTime,
                input = input,
                output = output
            ))
        }
    }

    /**
     * Convert string response to List<PrdTypeRecDTO> with proper exception handling
     */
    private fun convertStringToProductSelList(response: String?, requestId: String): List<PrdTypeRecDTO> {
        if (response.isNullOrBlank()) {
            logger.warn("Empty or null response received for requestId: $requestId")
            failCounter?.inc()
            return emptyList()
        }

        return try {
            logger.debug("Converting response to PrdTypeRecDTO list for requestId: $requestId")

            // First parse the response as a JSON object to extract schedule_by_day
            val responseJson = JsonUtil.fromJson(response, Map::class.java)
            if (responseJson == null) {
                logger.error("Failed to parse response as JSON object for requestId: $requestId")
                failCounter?.inc()
                return emptyList()
            }

            // Extract schedule_by_day from the response
            val scheduleByDay = responseJson["schedule_by_day"]
            if (scheduleByDay == null) {
                logger.warn("No 'schedule_by_day' key found in response for requestId: $requestId")
                failCounter?.inc()
                return emptyList()
            }

            logger.debug("Extracted schedule_by_day data for requestId: $requestId")

            // Convert schedule_by_day to JSON string and then to List<PrdTypeRecDTO>
            val scheduleByDayJson = JsonUtil.toJson(scheduleByDay)
            if (scheduleByDayJson.isNullOrBlank()) {
                logger.error("Failed to convert schedule_by_day to JSON string for requestId: $requestId")
                failCounter?.inc()
                return emptyList()
            }

            // Use JsonUtil with Result-based exception handling to parse the array
            JsonUtil.fromJsonArrayResult(scheduleByDayJson, PrdTypeRecDTO::class.java)
                .fold(
                    onSuccess = { result ->
                        val productSelList = result ?: emptyList()
                        successCounter?.inc()
                        // Process each PrdTypeRecDTO to assign scheduleDate to date field
                        val processedList = productSelList.map { prdTypeRecDTO ->
                            if (!prdTypeRecDTO.scheduleDate.isNullOrBlank()) {
                                prdTypeRecDTO.date = prdTypeRecDTO.scheduleDate.toString()
                                logger.debug("Assigned scheduleDate '${prdTypeRecDTO.scheduleDate}' to date field for dayId: ${prdTypeRecDTO.dayId}")
                            }
                            prdTypeRecDTO
                        }

                        logger.info("Successfully converted schedule_by_day to ${processedList.size} PrdTypeRecDTO items for requestId: $requestId")
                        processedList
                    },
                    onFailure = { exception ->
                        logger.error("Failed to convert schedule_by_day to PrdTypeRecDTO list for requestId: $requestId: ${exception.message}", exception)
                        logger.debug("Failed schedule_by_day content for requestId: $requestId: $scheduleByDayJson")
                        failCounter?.inc()
                        emptyList()
                    }
                )
        } catch (e: Exception) {
            logger.error("Unexpected error converting response to PrdTypeRecDTO list for requestId: $requestId: ${e.message}", e)
            logger.debug("Full response content for requestId: $requestId: $response")
            failCounter?.inc()
            emptyList()
        }
    }

    /**
     * Extract schedule data from AI response
     */
    private fun extractScheduleData(response: String): List<PrdTypeRecDTO> {
        try {
            logger.debug("Extracting schedule data from response: ${response.take(200)}...")

            // Check for opening tag
            if (!response.contains("<schedule_by_day>")) {
                logger.error("Response does not contain required <schedule_by_day> opening tag")
                return emptyList()
            }

            // Extract content after <schedule_by_day> tag
            // Handle cases where closing tag might be missing
            val scheduleByDay = if (response.contains("</schedule_by_day>")) {
                // Both tags present - extract content between them
                response.substringAfter("<schedule_by_day>")
                    .substringBefore("</schedule_by_day>")
            } else {
                // Only opening tag present - extract from opening tag to end
                logger.warn("Missing </schedule_by_day> closing tag, extracting from opening tag to end of response")
                response.substringAfter("<schedule_by_day>")
            }

            if (scheduleByDay.isBlank()) {
                logger.error("Empty content found after <schedule_by_day> tag")
                return emptyList()
            }

            // Extract JSON content between ```json and ```
            if (!scheduleByDay.contains("```json")) {
                logger.error("Schedule data does not contain required ```json opening marker")
                return emptyList()
            }

            val afterJsonMarker = scheduleByDay.substringAfter("```json")
            val jsonString = if (afterJsonMarker.contains("```")) {
                // Both markers present - extract content between them
                afterJsonMarker.substringBefore("```").trim()
            } else {
                // Only opening marker present - extract from opening marker to end
                logger.warn("Missing closing ``` marker, extracting from ```json to end of content")
                afterJsonMarker.trim()
            }

            if (jsonString.isBlank()) {
                logger.error("Empty JSON string extracted from response")
                return emptyList()
            }

            logger.debug("Extracted JSON string: $jsonString")

            // Parse JSON array with proper exception handling
            return JsonUtil.fromJsonArrayResult(jsonString, PrdTypeRecDTO::class.java)
                .fold(
                    onSuccess = { result ->
                        val scheduleItems = result ?: emptyList()
                        logger.info("Successfully parsed ${scheduleItems.size} schedule items")
                        scheduleItems
                    },
                    onFailure = { exception ->
                        logger.error("Failed to parse JSON array to PrdTypeRecDTO list: ${exception.message}", exception)
                        logger.debug("Failed JSON content: $jsonString")
                        emptyList()
                    }
                )

        } catch (e: Exception) {
            logger.error("Error extracting schedule data from AI response: ${e.message}", e)
            logger.debug("Full response content: $response")
            return emptyList()
        }
    }
}