package com.ctrip.ibu.userprofileserving.application.agent.cmt


import com.ctrip.ibu.userprofileserving.service.agent.AIClient
import com.ctrip.ibu.userprofileserving.service.config.UserPromptParse
import com.ctrip.ibu.userprofileserving.service.dao.entity.obkv.UserProfileTimeCost
import com.ctrip.ibu.userprofileserving.service.dto.CMTRecReqDTO
import com.ctrip.ibu.userprofileserving.service.dto.PrdTypeRecDTO
import com.ctrip.ibu.userprofileserving.service.dto.RecReasonDTO
import com.ctrip.ibu.userprofileserving.service.service.UserProfileCostTimeService
import com.ctrip.ibu.userprofileserving.service.util.Constant
import com.ctrip.ibu.userprofileserving.service.util.JsonUtil
import io.dropwizard.metrics5.Counter
import io.dropwizard.metrics5.MetricName
import io.dropwizard.metrics5.MetricRegistry
import jakarta.annotation.PostConstruct
import org.springframework.stereotype.Component
import qunar.tc.qconfig.client.TypedConfig

@Component
class RecReasonAgent(
    private val aiClient: AIClient,
    private val userProfileCostTimeService: UserProfileCostTimeService,
    private val metric: MetricRegistry
) {


    companion object {
        private val logger = org.slf4j.LoggerFactory.getLogger(RecReasonAgent::class.java)
        private const val RECOMMEND_REASON_PROMPT_CONFIG = "recommend-reason-prompt.yml"
        private const val ADD_RECOMMEND_REASONS_START_TAG = "<add_recommend_reasons>"
        private const val ADD_RECOMMEND_REASONS_END_TAG = "</add_recommend_reasons>"
        private const val JSON_CODE_BLOCK_START = "```json"
        private const val JSON_CODE_BLOCK_END = "```"
    }

    private var successCounter: Counter? = null

    private var failCounter: Counter? = null

    @PostConstruct
    fun init(){

        // add metric tags
        val tags = mutableMapOf<String, String>()
        tags["appid"] = Constant.APPID

        // Initialize counters for success and failure metrics
        successCounter = metric.counter(MetricName(Constant.CMT_REC_REASON_METRIC_SUCCESS, tags))
        failCounter = metric.counter(MetricName(Constant.CMT_REC_REASON_METRIC_FAIL, tags))
    }


    /**
     * Get recommendation reason data based on the description.
     */
    suspend fun getRecReasonData(
        req: CMTRecReqDTO,
        productSelData: List<PrdTypeRecDTO>,
        requestId: String
    ): List<RecReasonDTO> {
        val startTime = System.currentTimeMillis()
        val uid = req.uid

        logger.info("Generating recommendation reason data for uid: $uid, locale: ${req.locale},requestId: $requestId")

        var input: String? = null
        var output: String? = null

        return try {
            val prompts = buildRecommendationPrompts(req, productSelData)
            input = JsonUtil.toJson(prompts)

            logger.info("RecReasonAgent prompts generated for uid: $uid, requestId: $requestId, prompts: $prompts")

            val recommendReason = aiClient.generateResponse(prompts, Constant.VERTEX)
            output = recommendReason

            extractAndParseRecommendReasons(recommendReason)
        } catch (e: Exception) {
            logger.error("Failed to generate recommendation reason data for uid: $uid requestId:$requestId", e)
            failCounter?.inc()
            emptyList()
        } finally {
            recordCostTime(uid, requestId, startTime, input, output)
        }
    }

    /**
     * Build prompts for recommendation reason generation
     */
    private fun buildRecommendationPrompts(
        req: CMTRecReqDTO,
        productSelData: List<PrdTypeRecDTO>
    ): List<Map<String, String>> {
        val promptProperties = TypedConfig.get(RECOMMEND_REASON_PROMPT_CONFIG, UserPromptParse())
        val properties = promptProperties.current()

        val sb = StringBuilder()
        sb.append(properties.user ?: "")
        sb.append("\n用户行程:${JsonUtil.toJson(productSelData)}")
        sb.append("\nlocale:${req.locale}")
        val userPrompt = sb.toString()

        return listOf(mapOf("user" to userPrompt))
    }

    /**
     * Record cost time for performance monitoring
     */
    private fun recordCostTime(
        uid: String,
        requestId: String,
        startTime: Long,
        input: String?,
        output: String?
    ) {
        val costTime = System.currentTimeMillis() - startTime
        logger.info("Recommendation reason generation for uid: $uid completed in ${costTime}ms")

        userProfileCostTimeService.saveCostTime(
            UserProfileTimeCost(
                uid = uid,
                requestId = requestId,
                costType = Constant.REC_REASON_COST_TYPE,
                costTime = costTime,
                input = input,
                output = output
            )
        )
    }

    /**
     * Extract and parse recommendation reasons from AI response using JsonUtil
     */
    private fun extractAndParseRecommendReasons(recommendReason: String): List<RecReasonDTO> {
        return try {
            val jsonContent = extractJsonFromResponse(recommendReason)
            parseRecommendReasons(jsonContent)
        } catch (e: Exception) {
            logger.error("Failed to extract recommendation reason from AI response:{}", e)
            failCounter?.inc()
            emptyList()
        }
    }

    /**
     * Extract JSON content from AI response
     */
    private fun extractJsonFromResponse(response: String): String {
        val addRecommendReasons = response
            .split(ADD_RECOMMEND_REASONS_START_TAG)
            .getOrNull(1)
            ?.split(ADD_RECOMMEND_REASONS_END_TAG)
            ?.getOrNull(0)
            ?: throw IllegalArgumentException("Missing recommendation reasons tags in response")

        return addRecommendReasons
            .split(JSON_CODE_BLOCK_START)
            .getOrNull(1)
            ?.split(JSON_CODE_BLOCK_END)
            ?.getOrNull(0)
            ?.replace("\\", "") // Remove escape characters to reduce token usage
            ?: throw IllegalArgumentException("Missing JSON code block in response")
    }

    /**
     * Parse recommendation reasons from JSON string
     */
    private fun parseRecommendReasons(jsonContent: String): List<RecReasonDTO> {
        logger.info("Parsing recommendation reason JSON: $jsonContent")

        return JsonUtil.fromJsonArrayResult(jsonContent, RecReasonDTO::class.java)
            .onFailure { exception ->
                logger.error("Failed to parse recommendation reason JSON: $jsonContent", exception)
            }
            .onSuccess {
                logger.info("Successfully parsed recommendation reasons: $it")
                successCounter?.inc()
            }
            .getOrNull() ?: emptyList()
    }
}