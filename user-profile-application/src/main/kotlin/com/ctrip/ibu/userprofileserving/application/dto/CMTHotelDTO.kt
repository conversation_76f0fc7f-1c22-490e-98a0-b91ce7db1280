package com.ctrip.ibu.userprofileserving.application.dto

import com.ctrip.ibu.userprofileserving.service.dto.HotelFilterDTO
import com.ctrip.ibu.userprofileserving.soa.Head

/**
 * DTO classes for CMT Hotel operations
 */
data class CMTHotelRequest(
    val uid: String,
    val cityID: Int,
    val checkIn: String,
    val checkOut: String,
    val adultNum: Int = 1,
    val childNum: Int = 0,
    val currency: String = "CNY",
    val locale: String = "zh_CN",
    val platform: String = "PC",
    val group: String = "trip",
    val clientVersion: String = "999999",
    val clientId: String? = null,
    val requestId: String? = null,
    val returnCount: Int = 20,
    val filters: List<HotelFilterDTO>,
    val traceId: String,
    val head: Head
)

data class CMTHotelFilterRequest(
    val cityId: Int?,
    val checkIn: String?,
    val checkOut: String?,
    val traceId: String,
    val uid: String,
    val locale: String,
    val head: Head,
    val requestId: String? = null,
)

data class CMTHotelResponse(
    val hotels: List<Int>
)

data class HotelInfo(
    val isSoldOut: Boolean = false,
    var hotelName: String = "",
    var hotelEnglishName: String = "",
    val deletePrice: java.math.BigDecimal? = null,
    val displayPrice: java.math.BigDecimal? = null,
    val taxFeeInfos: List<String> = emptyList(),
    val coinsInfo: String? = null,
    val rightTags: List<String> = emptyList(),
    val powerTags: List<TagEntity> = emptyList(),
    val promotionTags: List<String> = emptyList(),
    val discountPercent: String? = null,
    val leftUpTag: String? = null,
)

data class TagEntity(
    val tagTitle: String? = null,
    val tagId: Int? = null
)
