package com.ctrip.ibu.userprofileserving.application.dto

data class FavoritesDTO(
    val fromCityId: Int,
    val toCityId: Int,
    val zone: String,
    val countryId: String,
    val cityName: String,


    val price: Double,
    val favoritePrice: Double,
    val currency: String,
    val star: Float,


    val bizType: String,
    val productType: String,
    val productTag: String,

    val commentScore: Float,
    val commentCount: Int,
    val favoriteCount: Int,

    val createTime: String,
    val powerTags: List<PowerTagDTO>,
)


data class PowerTagDTO(
    val tagId: Int,
    val tagTitle: String,
)