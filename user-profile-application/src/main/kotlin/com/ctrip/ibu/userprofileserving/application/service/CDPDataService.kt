package com.ctrip.ibu.userprofileserving.application.service

import com.ctrip.ibu.userprofileserving.application.soaclient.CDPDataServiceClient
import com.ctrip.ibu.userprofileserving.service.config.CommonConfig
import com.ctrip.ibu.userprofileserving.service.dao.entity.obkv.UserProfileTimeCost
import com.ctrip.ibu.userprofileserving.service.service.UserProfileCostTimeService
import com.ctrip.ibu.userprofileserving.service.util.Constant
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import qunar.tc.qconfig.client.spring.QMapConfig

@Service
class CDPDataService(
    private val cdpDataServiceClient: CDPDataServiceClient,
    private val userProfileCostTimeService: UserProfileCostTimeService
) {


    companion object {
        private val logger: Logger = LoggerFactory.getLogger(CDPDataService::class.java)
    }

    @QMapConfig(value = "common.properties")
    lateinit var commonConfig: CommonConfig


    fun getCDPData(uid: String, requestId: String): String {
        val startTime = System.currentTimeMillis()
        var result = ""
        var cdpData: Map<String, String> = emptyMap()
        try {
            logger.info("Getting CDP data for uid: $uid,requestId: $requestId")
            cdpData = cdpDataServiceClient.getCDPData(uid, requestId)
            if (cdpData.isEmpty()) {
                logger.warn("No CDP data found for uid: $uid, requestId: $requestId")
                return ""
            }
            logger.info("CDP data retrieved for uid: $uid, requestId: $requestId, data: $cdpData")
            val tagDescriptions = listOf(
                commonConfig.cdpMemberGradeTagId to "[Member Grade](1-Silver/2-Gold/3-Platinum/4-Diamond/5-Diamond+)",
                commonConfig.cdpAgeGroupTagId to "[Age Group]",
                commonConfig.cdpHotelSuccessOrdersTagId to "[Hotel Success Orders](Number of successful hotel orders submitted by the user)",
                commonConfig.cdpHotelPriceSensitivityTagId to "[Hotel Price Sensitivity](middle/high/low)",
                commonConfig.cdpHotelConsumptionCapacityTagId to "[Hotel Consumption Capacity](middle/high/low)",
                commonConfig.cdpBusinessTravelerTagId to "[Business Traveler](User used to have orders meet business travel characteristics):",
                commonConfig.cdpFamilyTypeTagId to "[Family Type](The tag covers users(identify by booking behaviors) who used to intend to travel with children)",
                commonConfig.cdpFamilyWithMultipleChildrenTagId to "[Family with Multiple Children](Among users who intend to travel with children, whether the family has multiple children)"
            )

            result = tagDescriptions.mapNotNull { (tagId, description) ->
                cdpData[tagId]?.let { value ->
                    when (tagId) {
                        commonConfig.cdpBusinessTravelerTagId,
                        commonConfig.cdpFamilyWithMultipleChildrenTagId -> {
                            when (value) {
                                "1" -> "- $description: yes"
                                "0" -> "- $description: no"
                                else -> null
                            }
                        }
                        else -> "- $description: $value"
                    }
                }
            }.joinToString("\n")
            return result
        } finally {
            val costTime = System.currentTimeMillis() - startTime
            logger.info("CDP data generation for uid $uid completed in $costTime ms,requestId: $requestId")
            userProfileCostTimeService.saveCostTime(
                UserProfileTimeCost(
                    uid = uid,
                    requestId = requestId,
                    costType = Constant.CMT_CDP_COST_TYPE,
                    costTime = costTime,
                    input = cdpData.toString(),
                    output = result
                )
            )
        }
    }


}