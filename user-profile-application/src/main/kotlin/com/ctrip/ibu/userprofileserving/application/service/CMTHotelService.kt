package com.ctrip.ibu.userprofileserving.application.service

import com.ctrip.hotel.wireless.Head
import com.ctrip.hotel.wireless.BasicGeography
import com.ctrip.hotel.wireless.hotelfrontfilteritemservice.HotelFilterItem
import com.ctrip.hotel.wireless.hotelfrontfilteritemservice.HotelFilterPackRequestType
import com.ctrip.hotel.wireless.hotelfrontfilteritemservice.PackSearchParam
import com.ctrip.hotel.wireless.hotelfronthotellistservice.*
import com.ctrip.ibu.userprofileserving.application.dto.CMTHotelRequest
import com.ctrip.ibu.userprofileserving.application.dto.CMTHotelResponse
import com.ctrip.ibu.userprofileserving.application.dto.CMTHotelFilterRequest
import com.ctrip.ibu.userprofileserving.application.soaclient.HotelFrontServiceClient
import com.ctrip.ibu.userprofileserving.service.config.CommonConfig
import com.ctrip.ibu.userprofileserving.service.dto.HotelFilterUnit
import com.ctrip.ibu.userprofileserving.service.util.DateUtils
import com.ctrip.ibu.userprofileserving.service.util.JsonUtil
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import qunar.tc.qconfig.client.spring.QMapConfig

@Service
class CMTHotelService(
    private val hotelFrontServiceClient: HotelFrontServiceClient
) {
    private val logger = LoggerFactory.getLogger(CMTHotelService::class.java)

    @QMapConfig(value = "common.properties")
    lateinit var commonConfig: CommonConfig

    /**
     * Get hotels with personalized filters based on user profile
     */
    fun getHotels(request: CMTHotelRequest): CMTHotelResponse {
        logger.info("Getting hotels for user ${request.uid} with request: ${JsonUtil.toJson(request)},requestId: ${request.requestId}")

        val hotelListRequest = createHotelListRequest(request)
        val response = hotelFrontServiceClient.getHotelList(hotelListRequest, request.requestId)
        return CMTHotelResponse(
            hotels = convertResponse(response)
        )
    }

    fun getHotelFilters(request: CMTHotelFilterRequest): List<HotelFilterUnit> {
        logger.info("Getting common filters for with request: $request,requestId: ${request.requestId}")
        val requestType = HotelFilterPackRequestType()
        requestType.setHead(buildHead(request.uid, request.locale, request.traceId, request.head))
        val search = PackSearchParam()
        search.checkin = request.checkIn
        search.checkout = request.checkOut
        search.location = buildLocation(request.cityId)
        search.clientParams = buildClientParams()
        search.token = commonConfig.hotelSearchToken
        requestType.setSearch(search)
        val response = hotelFrontServiceClient.getFilters(requestType,request.requestId)
        return extractAllFilterUnits(response.filters)
    }

    private fun extractAllFilterUnits(filterItems: List<HotelFilterItem>): List<HotelFilterUnit> {
        val result = mutableListOf<HotelFilterUnit>()

        for (item in filterItems) {
            item.data?.let { data ->
                if (!data.filterId.isNullOrEmpty() && !data.type.isNullOrEmpty()) {
                    val allowedTypes = commonConfig.allowFilterType?.split(",")?.map { it.trim() } ?: emptyList()
                    if (allowedTypes.isNotEmpty()) {
                        if (data.type in allowedTypes) {
                            result.add(
                                HotelFilterUnit(
                                    filterId = data.filterId,
                                    type = data.type,
                                    title = item.title,
                                    value = data.value
                                )
                            )
                        }
                    } else {
                        result.add(
                            HotelFilterUnit(
                                filterId = data.filterId,
                                type = data.type,
                                title = item.title,
                                value = data.value
                            )
                        )
                    }
                }
            }
            item.subItems?.let { subItems ->
                result.addAll(extractAllFilterUnits(subItems))
            }
        }

        return result
    }




    private fun buildClientParams():Map<String, String> {
       return mapOf("BrandShowType" to "8")
    }

    private fun buildLocation(cityId: Int?):com.ctrip.hotel.wireless.hotelfrontfilteritemservice.Location {
        val location = com.ctrip.hotel.wireless.hotelfrontfilteritemservice.Location()
        location.geo = BasicGeography().apply {
            setCityID(cityId)
        }
        return location
    }

    private fun createHotelListRequest(request: CMTHotelRequest): HotelFrontHotelListRequestType {
        val hotelListRequest = HotelFrontHotelListRequestType()
        hotelListRequest.setHead(buildHead(request.uid, request.locale, request.traceId, request.head))
        val search = createSearchWithDynamicFilters(request)
        hotelListRequest.setSearch(search)

        return hotelListRequest
    }

    private fun createSearchWithDynamicFilters(request: CMTHotelRequest): Search {
        return Search().apply {
            // Set check-in/check-out dates
            if (DateUtils.isFutureDate(request.checkIn)) {
                setCheckIn(request.checkIn.replace("-", ""))
                setCheckOut(request.checkOut.replace("-", ""))
            }
            setAdultNum(request.adultNum)
            setChildNum(request.childNum)
            // Set pagination
            setPageIndex(1)
            setPageSize(request.returnCount)

            setFilters(generateFilters(request))

            // Set location
            val location = Location().apply {
                val geo = BasicGeography().apply {
                    setCityID(request.cityID)
                }
                setGeo(geo)
            }
            setLocation(location)
        }
    }


    private fun generateFilters(request: CMTHotelRequest): List<HotelFilterBasicItem> {
        return request.filters.map {
            HotelFilterBasicItem().apply {
                setFilterId(it.filterId)
                setValue(it.value)
                setType(it.type)
            }
        }
    }

    private fun convertResponse(response: HotelFrontHotelListResponseType): List<Int> {
        return response.hotels?.map {
            it.summaryInfo.hotelBasicInfo.hotelId
        } ?: emptyList()
    }

    private fun buildHead(
        uid: String,
        locale: String,
        traceId: String,
        head: com.ctrip.ibu.userprofileserving.soa.Head
    ): Head {
        return Head().apply {
            setPlatform(head.platform)
            setClientVersion(head.clientVersion)
            setClientId(head.clientId)
            setUid(uid)
            setTraceLogID(traceId)
            setBu(head.bu)
            setGroup(head.group)
            setLocale(locale)
            setCurrency(head.currency)
        }
    }
}