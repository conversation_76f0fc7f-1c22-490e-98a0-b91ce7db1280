package com.ctrip.ibu.userprofileserving.application.service

import com.ctrip.ibu.userprofileserving.application.dto.FavoritesDTO
import com.ctrip.ibu.userprofileserving.application.dto.PowerTagDTO
import com.ctrip.ibu.userprofileserving.application.soaclient.MyFavoritesServiceClient
import com.ctrip.ibu.userprofileserving.service.util.DateUtils
import org.springframework.stereotype.Service

@Service
class FavoritesService(
    private val myFavoritesServiceClient: MyFavoritesServiceClient
) {

    companion object {
        private val logger = org.slf4j.LoggerFactory.getLogger(FavoritesService::class.java)
    }


    fun getMyFavorites(uid: String, requestId: String, startTime: String, endTime: String): List<FavoritesDTO> {
        logger.info("getMyFavorites request for uid: $uid, requestId: $requestId")
        val favorites = myFavoritesServiceClient.getMyFavorites(uid, requestId, startTime, endTime)
        // TODO Which specific DTO to return is still TBD by the algorithm team.
        return favorites.map {
            FavoritesDTO(
                fromCityId = it.fromCityID,
                toCityId = it.toCityID,
                zone = it.zone,
                countryId = it.extendList.find { it.name == "CountryID" }?.value ?: "",
                cityName = it.extendList.find { it.name == "CityName" }?.value ?: "",
                price = it.price,
                favoritePrice = it.favoritePrice,
                currency = it.currency,
                star = it.star,
                bizType = it.bizType,
                productType = it.productType,
                productTag = it.productTag,
                commentScore = it.commentScore,
                commentCount = it.commentCount,
                favoriteCount = it.favoriteCount,
                createTime = DateUtils.getCalendarDateTime(it.createTime) ?: "",
                powerTags = it.hotelInfo.powerTags.map { powerTag ->
                    PowerTagDTO(
                        tagId = powerTag.tagId,
                        tagTitle = powerTag.tagTitle
                    )
                }
            )
        }
    }
}