package com.ctrip.ibu.userprofileserving.application.service

/**
 * Hotel Filter Types and Values Documentation
 *
 * This class contains the documentation of available hotel filter types and their values.
 * These are used to generate dynamic filters based on user profile preferences.
 */
object HotelFilterTypes {

    /**
     * Filter type documentation that can be inserted into the LLM prompt
     */
    const val FILTER_TYPE_DOCUMENTATION = """
    You are assisting with generating hotel search filters based on user preferences. Follow these instructions precisely:

    OUTPUT FORMAT REQUIREMENTS:
    Return a valid JSON object with this exact structure:
    {
      "filters": [
        {"filterId": "TYPE|VALUE", "type": "TYPE", "value": "VALUE"}
      ]
    }

    FILTER SYNTAX RULES:
    1. filterId: Must follow format "TYPE|VALUE" (Example: "15|Range")
    2. type: Must be the numeric code only (Example: "15")
    3. value: Required format depends on filter type (Examples: "800|1200" for price range, "4" for star rating)
    4. All values must be strings, enclosed in double quotes in the JSON

    AVAILABLE FILTERS (Add only appropriate filters based on the user profile):
    
    1. Price Range Filter:
       - filterId: "15|Range"
       - type: "15"
       - value: "LOWER_PRICE|UPPER_PRICE" (Example: "800|1200")
       - When to use: When user expresses price sensitivity or budget preferences
    
    2. Breakfast Inclusion Filter:
       - filterId: "5|1"
       - type: "1"
       - value: "1"
       - When to use: When user mentions breakfast importance or history of booking with breakfast
    
    3. Star Rating Filter:
       - filterId: "16|STAR_RATING"
       - type: "16"
       - value: "STAR_RATING" (Example: "4" for 4-star, "5" for 5-star)
       - When to use: When user shows preference for specific hotel quality levels
       - Note: Add multiple separate filter objects for multiple star preferences

    IMPORTANT INSTRUCTIONS:
    1. DO NOT add explanation text or comments in the output
    2. DO NOT add filters without clear evidence in the user profile
    3. DO NOT modify the filter format or add additional fields
    4. Return ONLY the JSON structure - no other text
    5. Ensure all JSON is properly formatted and valid
    6. Leave out any filter entirely if there isn't sufficient user preference data
"""
    
    /**
     * Maps filter types to their human-readable names
     */
    val FILTER_TYPE_NAMES = mapOf(
        "3" to "Facilities",
        "15" to "Price Range",
        "29" to "Adult Child number",
        "16" to "Hotel Stars"
    )
    
    /**
     * Insert this filter documentation into the LLM prompt
     */
    fun getFilterDocumentationForPrompt(): String {
        return FILTER_TYPE_DOCUMENTATION
    }
} 