package com.ctrip.ibu.userprofileserving.application.soaclient

import com.ctrip.ibu.cdp.service.common.tag.CommonTagRequestType
import com.ctrip.ibu.cdp.service.soa.CdpdataserviceClient
import com.ctrip.ibu.userprofileserving.service.config.CommonConfig
import com.ctrip.ibu.userprofileserving.service.util.Constant
import com.ctrip.ibu.userprofileserving.service.util.RetryUtils
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import qunar.tc.qconfig.client.spring.QMapConfig

@Service
class CDPDataServiceClient {

    companion object {
        private val logger = LoggerFactory.getLogger(CDPDataServiceClient::class.java)

        private val client: CdpdataserviceClient = CdpdataserviceClient.getInstance().apply {
            format = "json"
        }
    }

    @QMapConfig(value = "common.properties")
    lateinit var commonConfig: CommonConfig

    /**
     * Fetches the CDP (Customer Data Platform) tag data associated with a specific user with timeout retry mechanism.
     *
     * @param uid The unique identifier for the user whose CDP data is to be retrieved.
     * @return A map containing the retrieved CDP tag data where the key is the tag ID and the value is the tag value.
     * Returns an empty map if no data is found or if all retries fail.
     */
    fun getCDPData(uid: String, requestId: String): Map<String, String> {
        logger.info("getCDPData request for uid: $uid")

        return RetryUtils.executeWithRetryAndDefaultResponse(
            operation = "getCDPData",
            createDefaultResponse = { emptyMap() }
        ) {
            val req = CommonTagRequestType().apply {
                this.idType = "uid"
                this.appId = Constant.APPID
                this.id = uid
                this.tagList = listOf(
                    commonConfig.cdpMemberGradeTagId,
                    commonConfig.cdpAgeGroupTagId,
                    commonConfig.cdpHotelSuccessOrdersTagId,
                    commonConfig.cdpHotelPriceSensitivityTagId,
                    commonConfig.cdpHotelConsumptionCapacityTagId,
                    commonConfig.cdpBusinessTravelerTagId,
                    commonConfig.cdpFamilyTypeTagId,
                    commonConfig.cdpFamilyWithMultipleChildrenTagId
                )
            }
            val result = client.getCommonTag(req)
            if (result.tagData.isNullOrEmpty()) {
                logger.error("No CDP data found for uid: $uid, requestId: $requestId")
                emptyMap()
            } else {
                result.tagData
            }
        }
    }
}