package com.ctrip.ibu.userprofileserving.application.soaclient

import com.ctrip.framework.spring.boot.soa.SOAClient
import com.ctrip.ibu.cs.llm.workbench.service.client.IbuCSLlmWorkbenchService
import com.ctrip.ibu.cs.llm.workbench.service.client.chatprompt.ChatPromptGenerateAndRunRequestType
import com.ctrip.ibu.cs.llm.workbench.service.client.common.ChatPromptRequestContext
import com.ctrip.ibu.userprofileserving.service.config.CmLLMConfig
import io.dropwizard.metrics5.Counter
import io.dropwizard.metrics5.MetricName
import io.dropwizard.metrics5.MetricRegistry
import jakarta.annotation.PostConstruct
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import qunar.tc.qconfig.client.spring.QMapConfig
import java.util.UUID
import java.util.concurrent.TimeoutException

@Service
class CsLLMServiceClient(
    private val metric: MetricRegistry,
) {

    @SOAClient
    lateinit var ibuCSLlmWorkbenchService: IbuCSLlmWorkbenchService

    companion object {
        private val logger = LoggerFactory.getLogger(CsLLMServiceClient::class.java)
        private const val SUCCESS_CODE = "200"
        private const val STATUS_CODE = 200
    }

    @QMapConfig(value = "cm-llm.properties")
    lateinit var cmLLMConfig: CmLLMConfig

    private var successCounter: Counter? = null

    private var failCounter: Counter? = null

    @PostConstruct
    fun init() {
        // Initialize counters for success and failure metrics
        successCounter = metric.counter(MetricName("cm_llm_request_count_success", mapOf("appid" to "100053425")))
        failCounter = metric.counter(MetricName("cm_llm_request_count_fail", mapOf("appid" to "100053425")))
    }


    /**
     * Generates and runs a chat prompt using the LLM service.
     * If the service returns a timeout or an error, it will retry the request up to a maximum number of attempts.
     * @param prompt The prompt to be sent to the LLM service.
     * @param data A map of configuration data to be used in the request.
     * @return The response message from the LLM service.
     * @throws RuntimeException if the LLM service returns an error or if there is a timeout.
     */
    fun generateAndRunChatPrompt(prompt: String, data: Map<String, String>): String {
        val request = createChatPromptRequest(prompt, data)

        repeat(cmLLMConfig.maxRetries) { attempt ->
            try {
                return processLlmServiceResponse(ibuCSLlmWorkbenchService.generateAndRunChatPrompt(request))
            } catch (e: TimeoutException) {
                handleRetryableException(e, attempt)
            } catch (e: java.net.SocketTimeoutException) {
                handleRetryableException(e, attempt)
            } catch (e: Exception) {
                logger.error("Error calling LLM service: ${e.message}", e)
                failCounter?.inc()
                throw e
            }
        }

        throw RuntimeException("Failed after ${cmLLMConfig.maxRetries} retries")
    }

    private fun createChatPromptRequest(prompt: String, data: Map<String, String>): ChatPromptGenerateAndRunRequestType =
        ChatPromptGenerateAndRunRequestType().apply {
            promptCode = prompt
            configs = data
            context = ChatPromptRequestContext().apply {
                token = cmLLMConfig.token
                appId = cmLLMConfig.appId
                projectId = cmLLMConfig.projectId
                traceId = UUID.randomUUID().toString()
                timeout = cmLLMConfig.timeout
            }
        }

    private fun processLlmServiceResponse(result: com.ctrip.ibu.cs.llm.workbench.service.client.chatprompt.ChatPromptGenerateAndRunResponseType): String {
        return when (result.resultCode) {
            SUCCESS_CODE -> handleSuccessResponse(result)
            else -> {
                logger.error("error:${result.resultMsg}")
                failCounter?.inc()
                throw RuntimeException("LLM service error: ${result.resultMsg}")
            }
        }
    }

    private fun handleSuccessResponse(result: com.ctrip.ibu.cs.llm.workbench.service.client.chatprompt.ChatPromptGenerateAndRunResponseType): String {
        return when (result.result.status) {
            STATUS_CODE -> {
                logger.info("message:${result.result.message}")
                successCounter?.inc()
                result.result.message
            }
            else -> {
                val message = result.result.message
                if (message.contains("TimeoutException")) {
                    logger.error("TimeoutException: $message")
                    failCounter?.inc()
                    throw TimeoutException("LLM service timeout: $message")
                } else {
                    logger.error("LLM service error: $message")
                    failCounter?.inc()
                    throw RuntimeException("LLM service error: $message")
                }
            }
        }
    }

    private fun handleRetryableException(e: java.lang.Exception, attempt: Int) {
        val currentAttempt = attempt + 1
        if (currentAttempt < cmLLMConfig.maxRetries) {
            logger.warn("timeout (attempt $currentAttempt/${cmLLMConfig.maxRetries}): ${e.message}")
            Thread.sleep(1000L * currentAttempt)
        } else {
            logger.error("timeout after ${cmLLMConfig.maxRetries} retries: ${e.message}", e)
            failCounter?.inc()
            throw e
        }
    }

}