package com.ctrip.ibu.userprofileserving.application.soaclient

import com.ctrip.hotel.wireless.hotelfrontfilteritemservice.HotelFilterPackRequestType
import com.ctrip.hotel.wireless.hotelfrontfilteritemservice.HotelFilterPackResponseType
import com.ctrip.hotel.wireless.hotelfrontfilteritemservice.HotelFrontFilterItemServiceClient
import com.ctrip.hotel.wireless.hotelfronthotellistservice.HotelFrontHotelListRequestType
import com.ctrip.hotel.wireless.hotelfronthotellistservice.HotelFrontHotelListResponseType
import com.ctrip.hotel.wireless.hotelfronthotellistservice.HotelFrontHotelListServiceClient
import com.ctrip.ibu.userprofileserving.service.util.JsonUtil
import com.ctrip.ibu.userprofileserving.service.util.RetryUtils
import jakarta.annotation.PostConstruct
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

@Service
class HotelFrontServiceClient {

    companion object {
        private val logger = LoggerFactory.getLogger(HotelFrontServiceClient::class.java)

        private val client: HotelFrontHotelListServiceClient = HotelFrontHotelListServiceClient.getInstance().apply {
            format = "json"
        }
        private val filtersClient: HotelFrontFilterItemServiceClient =
            HotelFrontFilterItemServiceClient.getInstance().apply {
                format = "json"
            }
    }

    @PostConstruct
    fun init() {
        client.setSocketTimeout("getHotelList", 10 * 1000)
    }

    /**
     * Get hotel list interface with timeout retry mechanism
     * @param request The request object containing hotel list parameters
     * @return HotelFrontHotelListResponseType containing the response data
     */
    fun getHotelList(request: HotelFrontHotelListRequestType, requestId: String?): HotelFrontHotelListResponseType {
        logger.info("getHotelList request: {}, requestId:$requestId", JsonUtil.toJson(request))

        return RetryUtils.executeWithRetryAndDefaultResponse(
            operation = "getHotelList",
            createDefaultResponse = { HotelFrontHotelListResponseType() }
        ) { client.getHotelList(request) }
    }

    /**
     * Get hotel filters with timeout retry mechanism
     * @param request The request object containing filter parameters
     * @return HotelFilterPackResponseType containing the response data
     */
    fun getFilters(request: HotelFilterPackRequestType, requestId: String?): HotelFilterPackResponseType {
        logger.info("getFilters request: {},requestId:$requestId", JsonUtil.toJson(request))

        return RetryUtils.executeWithRetryAndDefaultResponse(
            operation = "getFilters",
            createDefaultResponse = { HotelFilterPackResponseType() }
        ) { filtersClient.pack(request) }
    }
}