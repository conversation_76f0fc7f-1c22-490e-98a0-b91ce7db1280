package com.ctrip.ibu.userprofileserving.application.soaclient

import com.ctrip.soa.platform.account.promocodeservice.data.v1.UserCouponPromotionStrategyItem
import com.ctrip.soa.platform.account.promocodeservice.message.v1.SelectUserCouponWithPageRequestType
import com.ctrip.soa.platform.account.promocodeservice.v1.PromocodeServiceClient
import org.springframework.stereotype.Service

@Service
class MyCouponServiceClient {

    companion object {
        private val logger = org.slf4j.LoggerFactory.getLogger(MyCouponServiceClient::class.java)
        private val myCouponServiceClient = PromocodeServiceClient.getInstance().apply {
            format = "json"
        }
        private const val RESULT_CODE_SUCCESS = 0
        private val COUPON_STATUS_LIST = listOf(0, 1, 2) // 0:未使用, 1:已使用, 2:已过期
    }


    fun getMyCoupons(uid: String, requestId: String, endDate: String): List<UserCouponPromotionStrategyItem> {
        logger.info("getMyCoupons request for uid: $uid, requestId: $requestId")
        return try {
            val request = buildCouponRequest(uid, endDate)
            val response = myCouponServiceClient.selectUserCouponWithPage(request)
            if (RESULT_CODE_SUCCESS.toShort() != response.code) {
                logger.error("Failed to fetch coupons for uid: $uid, requestId: $requestId, resultCode: ${response.code}, error: ${response.message}")
                emptyList()
            } else {
                response.promotionStrategyList
            }
        } catch (e: Exception) {
            logger.error("Exception while fetching coupons for uid: $uid, requestId: $requestId", e)
            emptyList()
        }
    }

    private fun buildCouponRequest(uid: String, endDate: String): SelectUserCouponWithPageRequestType {
        return SelectUserCouponWithPageRequestType().apply {
            this.customerID = uid
            this.couponStatus = 0 // 0:未使用, 1:已使用, 2:已过期
            this.pageSize = 10
            this.startIndex = 1
            this.endDate = endDate
            this.currencyEnumValue = "-1"
            this.isWhetherDisableDisplayOnFrontEnd = true
        }
    }
}