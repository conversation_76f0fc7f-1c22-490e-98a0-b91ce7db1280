package com.ctrip.ibu.userprofileserving.application.soaclient

import com.ctrip.ibu.mytriporder.service.contract.Favorite
import com.ctrip.ibu.mytriporder.service.contract.GetFavoritesQuery
import com.ctrip.ibu.mytriporder.service.contract.GetMyFavoritesInternalRequest
import com.ctrip.ibu.mytriporder.service.contract.IbuMyTripOrderServiceClient
import org.springframework.stereotype.Service

@Service
class MyFavoritesServiceClient {

    companion object {
        private val logger = org.slf4j.LoggerFactory.getLogger(MyFavoritesServiceClient::class.java)

        private val myFavoritesServiceClient = IbuMyTripOrderServiceClient.getInstance().apply {
            format = "json"
        }
        private const val RESULT_CODE_SUCCESS = 0
    }


    fun getMyFavorites(uid: String, requestId: String, startTime: String, endTime: String): List<Favorite> {
        logger.info("getMyFavorites request for uid: $uid, requestId: $requestId")

        return try {
            val favoritesRequest = buildFavoritesRequest(uid, startTime, endTime)
            val response = myFavoritesServiceClient.getMyFavoritesInternal(favoritesRequest)
            if (RESULT_CODE_SUCCESS != response.resultCode) {
                logger.error("Failed to fetch favorites for uid: $uid, requestId: $requestId, resultCode: ${response.resultCode}, error: ${response.resultMessage}")
                return emptyList()
            }
            return response.favoriteList
        } catch (e: Exception) {
            logger.error("Exception while fetching favorites for uid: $uid, requestId: $requestId", e)
            emptyList()
        }
    }

    private fun buildFavoritesRequest(
        uid: String,
        startTime: String,
        endTime: String
    ): GetMyFavoritesInternalRequest {
        val request: GetMyFavoritesInternalRequest = GetMyFavoritesInternalRequest().apply {
            val query = GetFavoritesQuery()
            query.bizType = "ALL"
            query.citys = emptyList()
            this.uid = uid
            this.startTime = startTime
            this.endTime = endTime
            this.priceType = "0"
            this.queryList = listOf(query)
            this.returnCount = 100
        }
        return request
    }

}