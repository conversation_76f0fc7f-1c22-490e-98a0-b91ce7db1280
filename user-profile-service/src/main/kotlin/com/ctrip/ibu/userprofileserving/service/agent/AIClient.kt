package com.ctrip.ibu.userprofileserving.service.agent

import com.ctrip.ibu.userprofileserving.service.config.AIConfig
import com.ctrip.ibu.userprofileserving.service.util.Constant
import com.ctrip.ibu.userprofileserving.service.util.JsonUtil
import com.ctrip.infosec.kms.KmsUtilCache
import com.google.api.client.http.apache.ApacheHttpTransport
import com.google.auth.oauth2.GoogleCredentials
import com.google.genai.Client
import com.google.genai.types.Content
import com.google.genai.types.GenerateContentConfig
import com.google.genai.types.Part
import com.google.genai.types.ThinkingConfig
import io.dropwizard.metrics5.Counter
import io.dropwizard.metrics5.MetricName
import io.dropwizard.metrics5.MetricRegistry
import io.github.sashirestela.cleverclient.client.OkHttpClientAdapter
import io.github.sashirestela.openai.SimpleOpenAI
import io.github.sashirestela.openai.domain.chat.ChatMessage
import io.github.sashirestela.openai.domain.chat.ChatRequest
import io.github.sashirestela.openai.domain.chat.ChatRequest.ChatRequestBuilder
import jakarta.annotation.PostConstruct
import kotlinx.coroutines.*
import okhttp3.ConnectionPool
import okhttp3.OkHttpClient
import org.apache.commons.lang.StringUtils
import org.apache.http.HttpHost
import org.apache.http.conn.params.ConnRoutePNames
import org.apache.http.impl.client.DefaultHttpClient
import org.apache.http.impl.client.HttpClientBuilder
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import qunar.tc.qconfig.client.spring.QMapConfig
import java.io.ByteArrayInputStream
import java.nio.charset.StandardCharsets
import java.util.concurrent.TimeUnit


@Component
class AIClient(
    private val metric: MetricRegistry,
) {

    companion object {
        private const val DEFAULT_MAX_RETRIES = 5
        private const val DEFAULT_RETRY_DELAY_MS = 1000
        private val logger = LoggerFactory.getLogger(AIClient::class.java)
    }

    private var vertexClient: Client? = null

    private var emossClient: SimpleOpenAI? = null

    private var successCounter: Counter? = null

    private var failCounter: Counter? = null

    @QMapConfig(value = "ai.properties")
    lateinit var aiConfig: AIConfig


    @PostConstruct
    fun init() {
        val client = OkHttpClient.Builder()
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .writeTimeout(30, TimeUnit.SECONDS)
            .retryOnConnectionFailure(true)
            .connectionPool(ConnectionPool(15, 5, TimeUnit.MINUTES))
            .build()

        // add metric tags
        val tags = mutableMapOf<String, String>()
        tags["appid"] = Constant.APPID

        // Initialize counters for success and failure metrics
        successCounter = metric.counter(MetricName("ai_client_request_count_success", tags))
        failCounter = metric.counter(MetricName("ai_client_request_count_fail", tags))

        emossClient = SimpleOpenAI.builder()
            .apiKey(aiConfig.apikey ?: "")
            .baseUrl(aiConfig.baseurl ?: "")
            .clientAdapter(OkHttpClientAdapter(client))
            .build()

        vertexClient = initVertexClient()

        logger.info(
            "AIClient initialized with model: {}, max retries: {}",
            aiConfig.model, DEFAULT_MAX_RETRIES
        )
    }

    /**
     * Generates a response from AI based on the provided prompts.
     *
     * Implements a simple retry with fixed delay. If the current thread is interrupted
     * during sleep or remote call, we stop retrying immediately to propagate cancellation.
     */
    suspend fun generateResponse(
        prompts: List<Map<String, String>>,
        clientName: String = Constant.EMOSS
    ): String {
        // Check if AI responses should be mocked
        if (aiConfig.isMockResp) {
            return mockAIResponse()
        }

        for (attempt in 1..DEFAULT_MAX_RETRIES) {
            try {
                when (clientName.lowercase()) {
                    Constant.EMOSS -> {
                        logger.debug("Attempt $attempt/$DEFAULT_MAX_RETRIES: Calling Emoss API")

                        val chatRequest = buildChatRequest(prompts)
                        val response = withContext(Dispatchers.IO) {
                            emossClient?.chatCompletions()
                                ?.create(chatRequest)
                                ?.join()
                        }?.firstContent() ?: throw IllegalStateException(
                            "Emoss client not initialized or returned null"
                        )

                        if (response.isNotBlank()) {
                            logger.info("Emoss AI response: $response")
                            successCounter?.inc()
                            return response
                        }
                        logger.warn("Received empty response from Emoss API on attempt $attempt")
                    }

                    Constant.VERTEX -> {
                        logger.debug("Attempt $attempt/$DEFAULT_MAX_RETRIES: Calling Vertex API")

                        try {
                            val (userPrompt, config) = buildVertexChatRequest(prompts)
                            val response = withContext(Dispatchers.IO) {
                                vertexClient?.models
                                    ?.generateContent(aiConfig.geminiModel, userPrompt, config)
                                    ?.text()
                            }

                            if (!response.isNullOrBlank()) {
                                logger.info("Vertex AI response: $response")
                                successCounter?.inc()
                                return response
                            }
                            logger.warn("Received empty response from Vertex API on attempt $attempt")
                        } catch (e: Exception) {
                            if (isServerError(e)) {
                                logger.error("Vertex API returned 5xx error: ${e.message}, attempting fallback to Emoss")

                                try {
                                    val fallbackResponse = callEmossAsFallback(prompts)
                                    if (fallbackResponse.isNotBlank()) {
                                        logger.info("Successfully switched to Emoss as fallback: $fallbackResponse")
                                        successCounter?.inc()
                                        return fallbackResponse
                                    }
                                } catch (fallbackException: Exception) {
                                    logger.error("Emoss fallback also failed: ${fallbackException.message}", fallbackException)
                                }
                            }
                            // other exceptions are logged and retried
                            throw e
                        }
                    }

                    else -> {
                        logger.error("Unknown client name: $clientName")
                        throw IllegalArgumentException(
                            "Unknown client name: $clientName. Supported clients: emoss, vertex"
                        )
                    }
                }
            } catch (ce: CancellationException) {
                // 特殊处理协程取消异常
                logger.error("Coroutine cancelled during attempt $attempt - aborting retries")
                failCounter?.inc()
                throw ce  // 重新抛出取消异常，不要尝试重试
            } catch (e: Exception) {
                logger.error("Error on attempt $attempt/$DEFAULT_MAX_RETRIES: ${e.message}", e)
                failCounter?.inc()

                // 检查线程是否被中断
                if (Thread.currentThread().isInterrupted) {
                    logger.warn("Thread interrupted – aborting further retries")
                    break
                }

                // 如果不是最后一次尝试，则休眠
                if (attempt < DEFAULT_MAX_RETRIES) {
                    try {
                        delay(DEFAULT_RETRY_DELAY_MS.toLong())
                    } catch (ce: CancellationException) {
                        logger.warn("Delay cancelled during retry delay")
                        throw ce  // 重新抛出取消异常，不要尝试继续重试
                    }
                }
            }
        }

        logger.error(
            "Failed to generate response after $DEFAULT_MAX_RETRIES attempts with client: $clientName"
        )
        return ""
    }

    /**
     * Creates a mock AI response for testing purposes.
     */
    fun mockAIResponse(): String {
        Thread.sleep(3000)
        return "this is a mock response".also {
            logger.info("Mock response: $it")
        }
    }

    /**
     * Builds a chat request object from the provided prompts.
     */
    private fun buildChatRequest(prompts: List<Map<String, String>>): ChatRequest {
        val builder = ChatRequest.builder()
            .model(aiConfig.model)
            .temperature(aiConfig.temperature)
            .maxCompletionTokens(aiConfig.maxTokens)

        prompts.forEach { prompt ->
            prompt.forEach { (role, content) ->
                addMessageByRole(builder, role, content)
            }
        }

        return builder.build()
    }

    private fun buildVertexChatRequest(prompts: List<Map<String, String>>): Pair<String, GenerateContentConfig> {
        val (systemPrompt, userPrompt) = extractPrompts(prompts)

        val config = buildGenerateContentConfig(systemPrompt)

        return userPrompt to config
    }

    private fun extractPrompts(prompts: List<Map<String, String>>): Pair<String, String> {
        var systemPrompt = StringUtils.EMPTY
        var userPrompt = StringUtils.EMPTY

        prompts.forEach { prompt ->
            prompt.forEach { (role, content) ->
                when (role) {
                    "system" -> systemPrompt = content
                    "user" -> userPrompt = content
                    else -> logger.warn("Unsupported message role: '$role' - skipping this message")
                }
            }
        }

        return systemPrompt to userPrompt
    }

    private fun buildGenerateContentConfig(systemPrompt: String): GenerateContentConfig {
        return GenerateContentConfig.builder().apply {
            if (systemPrompt.isNotBlank()) {
                systemInstruction(Content.fromParts(Part.fromText(systemPrompt)))
            }
            if (aiConfig.isThinking) {
                thinkingConfig(ThinkingConfig.builder().thinkingBudget(0).build())
            }
            temperature(0.0f)
            labels(
                mapOf(
                    "project" to Constant.GEMINI_LABEL_PROJECT,
                    "appId" to Constant.APPID
                )
            )
        }.build()
    }


    private fun initVertexClient(): Client {
        return try {
            val credentials = retrieveCredentials()

            val client = buildVertexClient(credentials)

            configureClientProxy(client)

            client
        } catch (e: Exception) {
            logger.error("Error initializing Gemini client: ${e.message}", e)
            throw RuntimeException("Failed to initialize Gemini client", e)
        }
    }

    private fun retrieveCredentials(): GoogleCredentials {
        val kmsResult = KmsUtilCache.getPwd(aiConfig.kmsAppId, aiConfig.kmsToken)
        logger.info(
            "retrieveCredentials:code: {}, message: {},result:{}",
            kmsResult.code,
            kmsResult.message,
            JsonUtil.toJson(kmsResult.result)
        )
        return kmsResult.result.pwdValue
            .byteInputStream(StandardCharsets.UTF_8)
            .use { getCredentials(it) }
    }

    private fun buildVertexClient(credentials: GoogleCredentials): Client =
        Client.builder()
            .credentials(credentials)
            .project(aiConfig.geminiProject)
            .location(aiConfig.geminiLocation)
            .vertexAI(true)
            .build()

    private fun configureClientProxy(client: Client) {
        val apiClientField = Client::class.java.getDeclaredField("apiClient").apply {
            isAccessible = true
        }
        val apiClientInstance = apiClientField.get(client)

        val proxiedHttpClient = HttpClientBuilder.create()
            .setProxy(HttpHost(aiConfig.proxyHost, aiConfig.proxyPort))
            .setConnectionManagerShared(true)
            .build()

        val apiClientClass = Class.forName("com.google.genai.ApiClient")
        val httpField = apiClientClass.getDeclaredField("httpClient").apply {
            isAccessible = true
        }

        httpField.set(apiClientInstance, proxiedHttpClient)
    }

    private fun getCredentials(stream: ByteArrayInputStream): GoogleCredentials {
        return GoogleCredentials.fromStream(stream) {
            val httpClient = DefaultHttpClient()
            httpClient.params.setParameter(
                ConnRoutePNames.DEFAULT_PROXY,
                HttpHost(aiConfig.proxyHost, aiConfig.proxyPort, "http")
            )
            ApacheHttpTransport(httpClient)
        }
    }

    /**
     * Adds a message to the chat request builder based on its role.
     */
    private fun addMessageByRole(builder: ChatRequestBuilder, role: String, content: String?) {
        when (role) {
            "system" -> builder.message(ChatMessage.SystemMessage.of(content))
            "user" -> builder.message(ChatMessage.UserMessage.of(content))
            "assistant" -> builder.message(ChatMessage.AssistantMessage.of(content))
            "developer" -> builder.message(ChatMessage.DeveloperMessage.of(content))
            else -> logger.warn("Unsupported message role: '$role' - skipping this message")
        }
    }
    /**
     * Checks if the exception indicates a server error (5xx HTTP status codes).
     */
    private fun isServerError(exception: Exception): Boolean {
        val message = exception.message ?: ""
        val stackTrace = exception.stackTraceToString()

        // List of common server error patterns
        val serverErrorPatterns = listOf(
            "500", "501", "502", "503", "504", "505", "507"
        )

        return serverErrorPatterns.any { pattern ->
            message.contains(pattern, ignoreCase = true) || stackTrace.contains(pattern, ignoreCase = true)
        }
    }


    /**
     * Calls Emoss as a fallback when Vertex API fails.
     * This function is used to ensure that we can still get a response even if the primary Vertex API fails.
     */
    private suspend fun callEmossAsFallback(prompts: List<Map<String, String>>): String {
        logger.info("Calling Emoss as fallback for Vertex API failure")

        val chatRequest = buildChatRequest(prompts)
        val response = withContext(Dispatchers.IO) {
            emossClient?.chatCompletions()
                ?.create(chatRequest)
                ?.join()
        }?.firstContent() ?: throw IllegalStateException(
            "Emoss client not initialized or returned null during fallback"
        )
        logger.info("Emoss fallback response: $response")
        return response
    }
}