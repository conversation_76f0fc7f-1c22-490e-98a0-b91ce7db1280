package com.ctrip.ibu.userprofileserving.service.agent

import com.ctrip.arch.distlock.DistributedLockService
import com.ctrip.arch.distlock.exception.DistlockRejectedException
import com.ctrip.ibu.userprofileserving.service.config.PromptConfig
import com.ctrip.ibu.userprofileserving.service.config.PromptParse
import com.ctrip.ibu.userprofileserving.service.config.SummaryConfig
import com.ctrip.ibu.userprofileserving.service.dao.entity.obkv.UserProfileTimeCost
import com.ctrip.ibu.userprofileserving.service.dto.DetailL2DTO
import com.ctrip.ibu.userprofileserving.service.dto.SummaryL3DTO
import com.ctrip.ibu.userprofileserving.service.service.UbtDataService
import com.ctrip.ibu.userprofileserving.service.service.UserProfileCostTimeService
import com.ctrip.ibu.userprofileserving.service.service.UserProfileSummaryService
import com.ctrip.ibu.userprofileserving.service.util.CommonUtil
import com.ctrip.ibu.userprofileserving.service.util.CommonUtil.cleanJsonForPrompt
import com.ctrip.ibu.userprofileserving.service.util.Constant
import com.ctrip.ibu.userprofileserving.service.util.DateUtils
import com.ctrip.ibu.userprofileserving.service.util.JsonUtil
import com.ctrip.ibu.userprofileserving.service.vo.DetailL2VO
import com.ctrip.ibu.userprofileserving.service.vo.SummaryL3VO
import jakarta.annotation.Resource
import org.slf4j.LoggerFactory
import org.slf4j.MDC
import org.springframework.stereotype.Component
import qunar.tc.qconfig.client.TypedConfig
import qunar.tc.qconfig.client.spring.QMapConfig
import java.text.MessageFormat
import java.util.concurrent.*
import kotlin.math.max
import kotlin.math.min

import kotlinx.coroutines.*
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.slf4j.MDCContext

@Component
class SummaryAgent(
    private val ubtDataService: UbtDataService,
    private val summaryService: UserProfileSummaryService,
    private val aiClient: AIClient,
    private val userProfileCostTimeService: UserProfileCostTimeService,
    private val distributedLockService: DistributedLockService
) {

    private val logger = LoggerFactory.getLogger(SummaryAgent::class.java)

    @QMapConfig("summary.properties")
    lateinit var summaryConfig: SummaryConfig

    @Resource(name = "SummaryAgentAsyncService")
    lateinit var asyncExecutor: ThreadPoolExecutor

    /**
     * Try to get the distributed lock, if failed, return an empty list
     * if success, call doSummary
     */
    suspend fun call(
        uid: String,
        callTime: String,
        topic: String,
        topicId: String,
        requestId: String
    ): List<SummaryL3VO> {
        val lockKey = "summary:lock:$uid:$callTime:$topicId"
        var locked = false
        val dLock = distributedLockService.getLock(lockKey)
        logger.info("uid call summary $uid, callTime $callTime, topic $topic, topicId $topicId")

        val startTime = System.currentTimeMillis()

        try {
            locked = dLock.tryLock()
            if (locked) {
                val result = doSummary(uid, callTime, topic, topicId, requestId)

                val costTime = System.currentTimeMillis() - startTime
                logger.info("Summary generation for uid $uid completed in $costTime ms")

                userProfileCostTimeService.saveCostTime(UserProfileTimeCost(
                    uid = uid,
                    requestId = requestId,
                    costType = Constant.L3_SUMMARY_COST_TYPE,
                    costTime = costTime,
                    input = null,
                    output = null
                ))

                return result
            } else {
                // Don't acquire the lock, return an empty list. Ensure summary logic is idempotent
                logger.warn(
                    "Failed to acquire distributed lock for $lockKey,uid:$uid,callTime $callTime,topic $topic," +
                            " topicId $topicId, returning empty list."
                )
                return emptyList()
            }
        } catch (e: DistlockRejectedException) {
            // Distributed lock service is down
            logger.error("Failed to acquire distributed lock: ${e.message}", e)
            return emptyList()
        } finally {
            if (!locked) {
                val costTime = System.currentTimeMillis() - startTime
                userProfileCostTimeService.saveCostTime(UserProfileTimeCost(
                    uid = uid,
                    requestId = requestId,
                    costType = Constant.L3_SUMMARY_COST_TYPE,
                    costTime = costTime,
                    input = null,
                    output = null
                ))
            }

            if (locked) {
                dLock.unlock()
            }
        }
    }

    /**
     * Remove summaries for a user within a date range
     */
    fun removeSummary(uid: String, dateFrom: String, dateTo: String, topicId: String) {
        logger.info("Removing summaries for user $uid from $dateFrom to $dateTo")
        summaryService.removeUserProfileSummaryData(
            uid,
            DateUtils.parseDateMillis(dateFrom),
            DateUtils.parseDateMillis(dateTo),
            topicId
        )
    }

    /**
     * Main method to generate summaries.
     * It fetches UBT data and existing summaries, processes them, and generates new summaries.
     */
    suspend fun doSummary(
        uid: String,
        callTime: String,
        topic: String,
        topicId: String,
        requestId: String
    ): List<SummaryL3VO> {
        // Add MDC info
        addRequestInfoToMDC(uid, callTime, topicId, requestId)

        // Validate input parameters
        val (startTime, endTime) = getTimeRange(callTime)

        // Fetch UBT data and existing summaries asynchronously
        val (ubtList, summaryList) = fetchUbtAndSummaryDataAsync(uid, startTime, endTime, topicId)

        // process UBT data and find missing ranges
        val (sortedUbtList, missingRanges) = processUbtAndFindMissingRanges(ubtList, summaryList)

        // Split missing ranges into chunks and prepare for processing
        val (chunkRanges, tailRanges) = prepareChunkRanges(missingRanges, sortedUbtList.size)

        // Generate new summaries with a thread pool
        val newSummaries = generateNewSummaries(topic, topicId, uid, sortedUbtList, chunkRanges, requestId)

        // Save new summaries asynchronously
        saveNewSummariesAsync(newSummaries)

        // Combine and return results
        val summaryVOList = combineAndSortSummaries(summaryList, newSummaries)

        // Split UBT data into chunks for margin calculation
//        val marginVOList = spiltUbtMargin(sortedUbtList, tailRanges[0], tailRanges[1])

        return summaryVOList
    }

    /**
     * Retrieves user profile summaries for a given time range and topic.
     * This function fetches existing summaries and details without generating new ones.
     *
     * @param uid The user ID to get summaries for
     * @param startTime The start time of the summary period (format: yyyy-MM-dd HH:mm:ss.SSS)
     * @param endTime The end time of the summary period (format: yyyy-MM-dd HH:mm:ss.SSS)
     * @param topicId The topic ID for the summaries
     * @return A pair containing lists of summaries and details
     */
    fun getSummary(
        uid: String,
        startTime: String,
        endTime: String,
        topicId: String
    ): Pair<List<SummaryL3VO>, List<DetailL2VO>> {
        logger.info("Getting user profile summary for uid: $uid, startTime: $startTime, endTime: $endTime")

        // Parse timestamps
        val startTimeMillis = DateUtils.parseDateTimeMillis(startTime)
        val endTimeMillis = DateUtils.parseDateTimeMillis(endTime)

        // Fetch existing summaries
        val summaryList = summaryService.getUserProfileSummaryData(uid, startTimeMillis, endTimeMillis, topicId)

        // Convert summaries to VO objects
        val summaryVOList = summaryList.map { summary ->
            SummaryL3VO(
                uid = summary.uid,
                summaryId = summary.summaryId,
                startTime = summary.startTime,
                endTime = summary.endTime,
                topic = "", // TODO[jianweidai]: Check what needs to be filled in this field
                summary = summary.summary
            )
        }

        // Fetch UBT data for the time range
        val ubtList = ubtDataService.getUserProfileUbtData(uid, startTimeMillis, endTimeMillis)
        
        // Convert UBT data to VO objects
        val detailVOList = ubtList.map { ubt ->
            DetailL2VO(
                uid = ubt.uid,
                startTime = ubt.startTime,
                value = ubt.value
            )
        }

        return Pair(summaryVOList, detailVOList)
    }

    suspend fun getL2ChunkRanges(
        uid: String,
        callTime: String,
        topic: String,
        topicId: String
    ): List<IntArray> {
        // Validate input parameters
        val (startTime, endTime) = getTimeRange(callTime)

        // Fetch UBT data and existing summaries asynchronously
        val (ubtList, summaryList) = fetchUbtAndSummaryDataAsync(uid, startTime, endTime, topicId)

        // process UBT data and find missing ranges
        val (sortedUbtList, missingRanges) = processUbtAndFindMissingRanges(ubtList, summaryList)

        // Split missing ranges into chunks and prepare for processing
        val (chunkRanges, tailRanges) = prepareChunkRanges(missingRanges, sortedUbtList.size)
        // Return the chunk ranges
        return chunkRanges
    }

    private fun getTimeRange(callTime: String): kotlin.Pair<Long, Long> {
        val startDateStr = DateUtils.getDateTime(callTime, summaryConfig.daysMax)
        val endDateStr = DateUtils.getDateTime(callTime, 0)

        val startTime = DateUtils.parseDateTimeMillis(startDateStr)
        val endTime = DateUtils.parseDateTimeMillis(endDateStr)
        logger.info("Start time: $startDateStr, End time: $endDateStr")
        return Pair(startTime, endTime)
    }

    private fun processUbtAndFindMissingRanges(
        ubtList: List<DetailL2DTO>,
        summaryList: List<SummaryL3DTO>
    ): Pair<List<DetailL2DTO>, List<IntArray>> {
        // Sort UBT data by start time
        val sortedUbtList = sortUbtListByStartTime(ubtList)

        // Extract time ranges from existing summaries
        val summaryTimeRanges = extractTimeRanges(summaryList)
        val formattedRanges = summaryTimeRanges.joinToString { it.contentToString() }
        logger.info("Summary time ranges: $formattedRanges")
        // Get UBT times and find missing ranges
        val ubtTimes = sortedUbtList.map { it.startTimeMills }
        logger.info("UBT times: $ubtTimes")
        val missingIndexes = CommonUtil.findMissingIndexes(ubtTimes, summaryTimeRanges)
        logger.info("Missing indexes: ${missingIndexes.toList()}")
        val missingRanges = CommonUtil.mergeContinuousNumbers(missingIndexes)
        logger.info("Missing ranges: ${missingRanges.map { it.toList() }}")
        return Pair(sortedUbtList, missingRanges)
    }

    private fun extractTimeRanges(summaryList: List<SummaryL3DTO>): List<Array<Long>> {
        if (summaryList.isEmpty()) {
            return emptyList()
        }
        val timeRanges = summaryList
            .sortedBy { DateUtils.parseDateTimeMillis(it.startTime) }
            .map { summary ->
                arrayOf(
                    DateUtils.parseDateTimeMillis(summary.startTime),
                    DateUtils.parseDateTimeMillis(summary.endTime)
                )
            }
        return CommonUtil.mergeTimeRanges(timeRanges)
    }

    private fun sortUbtListByStartTime(ubtList: List<DetailL2DTO>): List<DetailL2DTO> =
        ubtList.let { list ->
            if (list.isEmpty()) {
                emptyList()
            } else {
                list.sortedBy { it.startTimeMills }
            }
        }

    private fun prepareChunkRanges(
        missingRanges: List<IntArray>,
        listSize: Int
    ): Pair<List<IntArray>, IntArray> {
        val chunkRanges = missingRanges
            .flatMap { range ->
                chunkSplit(range[0], range[1] + 1, listSize)
            }
            .sortedByDescending { it[0] }
            .toMutableList()

        val tailSplit = when {
            chunkRanges.isEmpty() -> IntArray(2)
            else -> {
                val tailChunk = chunkRanges.first()
                val (tailChunkLeft, tailChunkRight) = tailChunk[0] to tailChunk[1]
                val minChunkSize = summaryConfig.chunkSizeMin

                if (tailChunkRight - tailChunkLeft > minChunkSize) {
                    chunkRanges[0] = intArrayOf(tailChunkLeft, tailChunkRight)
                    intArrayOf(tailChunkLeft + minChunkSize, listSize)
                } else {
                    chunkRanges.removeFirst()
                    intArrayOf(tailChunkLeft, tailChunkRight)
                }
            }
        }
        logger.info("Chunk ranges: ${chunkRanges.map { it.toList() }}, Tail split: ${tailSplit.toList()}")
        return Pair(chunkRanges, tailSplit)
    }

    private suspend fun generateNewSummaries(
        topic: String,
        topicId: String,
        uid: String,
        ubtList: List<DetailL2DTO>,
        chunkRanges: List<IntArray>,
        requestId: String
    ): List<SummaryL3DTO> = coroutineScope {
        val dispatcher = asyncExecutor.asCoroutineDispatcher()

        val allResults = chunkRanges.map { range ->
            async(dispatcher + MDCContext()) {
                generateSummaryForChunk(topic, topicId, range[0], range[1], uid, ubtList, requestId)
            }
        }

        val startTime = System.currentTimeMillis()
        val results = allResults.mapNotNull { deferred ->
            try {
                deferred.await()?.apply { isNew = true }
            } catch (e: Exception) {
                logger.error("Unexpected error generating summary for uid:$uid: ${e.message}", e)
                null
            }
        }
        val endTime = System.currentTimeMillis()
        logger.info(
            "All summary tasks completed for uid: $uid - Total execution time: ${endTime - startTime}ms; " +
                    "chunks: ${chunkRanges.size}, successful: ${results.size}"
        )

        results
    }

    private fun saveNewSummariesAsync(newSummaries: List<SummaryL3DTO>) {
        if (newSummaries.isNotEmpty()) {
            val mdcContext = MDC.getCopyOfContextMap() ?: emptyMap()
            CompletableFuture.runAsync {
                try {
                    MDC.setContextMap(mdcContext)
                    summaryService.batchSaveUserProfileSummaryData(newSummaries)
                } finally {
                    MDC.clear()
                }
            }
                .exceptionally { ex ->
                    logger.error("Failed to save summaries: ${ex.message}", ex)
                    null
                }
        }
    }

    private suspend fun fetchUbtAndSummaryDataAsync(
        uid: String,
        startTime: Long,
        endTime: Long,
        topicId: String
    ): Pair<List<DetailL2DTO>, List<SummaryL3DTO>> = coroutineScope {
        val dispatcher = asyncExecutor.asCoroutineDispatcher()

        val ubtDeferred = async(dispatcher) {
            ubtDataService.getUserProfileUbtData(uid, startTime, endTime)
        }
        val summaryDeferred = async(dispatcher) {
            summaryService.getUserProfileSummaryData(uid, startTime, endTime, topicId)
        }

        try {
            val ubtList = ubtDeferred.await()
            val summaryList = summaryDeferred.await()
            logger.info(
                "Asynchronous data fetch complete for UID: $uid startTime:$startTime endTime:$endTime " +
                        "- UBT size: ${ubtList.size}, Summary size: ${summaryList.size}"
            )
            Pair(ubtList, summaryList)
        } catch (e: Exception) {
            logger.error(
                "Failed to fetch data asynchronously for UID: $uid. Cause: ${e.message}",
                e
            )
            Pair(emptyList(), emptyList())
        }
    }

    private fun combineAndSortSummaries(
        existingSummaries: List<SummaryL3DTO>, newSummaries: List<SummaryL3DTO>
    ): List<SummaryL3VO> {
        val marked = existingSummaries.map { it.apply { isNew = false } }
        val combinedSummaries = (marked + newSummaries).sortedBy { it.startTime }
        // Convert to UserProfileSummaryVo
        logger.info(
            "existingSummaries size: ${existingSummaries.size}, newSummaries size: ${newSummaries.size}, " +
                    "combined size: ${combinedSummaries.size}"
        )
        return combinedSummaries.map { summary ->
            SummaryL3VO(
                uid = summary.uid,
                summaryId = summary.summaryId,
                startTime = summary.startTime,
                endTime = summary.endTime,
                topic = summary.topic,
                summary = summary.summary
            )
        }
    }

    private fun spiltUbtMargin(
        ubtList: List<DetailL2DTO>,
        tailSpitLeft: Int,
        tailSpitRight: Int
    ): List<DetailL2VO> {
        if (ubtList.isEmpty()) {
            return emptyList()
        }
        // Split UBT data into chunks
        val splitUbtList = ubtList.subList(tailSpitLeft, tailSpitRight)
        logger.info(
            "Split UBT list size: ${splitUbtList.size}, " +
                    "tailSpitLeft: $tailSpitLeft, tailSpitRight: $tailSpitRight"
        )
        return if (splitUbtList.isNotEmpty()) {
            val splitUbtDetailData = ubtDataService.getUbtByRowKeys(splitUbtList)
            splitUbtDetailData.map { ubt ->
                DetailL2VO(
                    uid = ubt.uid,
                    startTime = ubt.startTime,
                    value = ubt.value
                )
            }
        } else {
            emptyList()
        }
    }

    /**
     * Split data into chunks
     */
    private fun chunkSplit(idxStart: Int, idxEnd: Int, length: Int): List<IntArray> {
        val chunkRanges = mutableListOf<IntArray>()
        var chunkLeft = idxStart

        while (chunkLeft < idxEnd) {
            val chunkSize = summaryConfig.chunkSize
            val chunkOverlap = summaryConfig.chunkOverlap

            val chunkRight = min(length, chunkLeft + chunkSize + chunkOverlap)
            chunkLeft = max(0, chunkLeft - chunkOverlap)

            chunkRanges.add(intArrayOf(chunkLeft, chunkRight))
            chunkLeft += chunkSize
        }

        return chunkRanges
    }

    /**
     * Generates a summary for a specific chunk of UBT data.
     */
    private suspend fun generateSummaryForChunk(
        topic: String,
        topicId: String,
        chunkLeft: Int,
        chunkRight: Int,
        uid: String,
        ubtList: List<DetailL2DTO>,
        requestId: String
    ): SummaryL3DTO? {
        // Validate input parameters
        if (chunkLeft < 0 || chunkRight > ubtList.size || chunkLeft >= chunkRight) {
            logger.error("Uid:$uid, Invalid chunk range: [$chunkLeft, $chunkRight) for list size ${ubtList.size}")
            return null
        }

        return try {
            // Get UBT logs for the chunk
            val chunkUBTs = ubtList.subList(chunkLeft, chunkRight)
            logger.info("Uid:$uid, Summarize Chunk, Range=[$chunkLeft, $chunkRight) size=${chunkUBTs.size}, Total=${ubtList.size}")

            // Add some info to mdc
            addMdcInfo(chunkUBTs)

            // Get UBT detail value for the chunk
            val ubtDetailList = ubtDataService.getUbtByRowKeys(chunkUBTs)
            logger.info("Uid:$uid, Chunk UBT detail size: ${ubtDetailList.size}")

            // Generate prompt for OpenAI client
            val prompts = generatePrompt(topic, ubtDetailList)

            // Generate summary using the OpenAI client
            val startTime = System.currentTimeMillis()
            val summary = aiClient.generateResponse(prompts, Constant.VERTEX)
            val endTime = System.currentTimeMillis()
            logger.info("Uid:$uid Summarize Chunk,Range=[$chunkLeft, $chunkRight), Total=${ubtList.size}, AI generate time: ${endTime - startTime}ms")
            saveCostTime(uid, requestId, JsonUtil.toJson(prompts), summary, endTime - startTime, chunkLeft, chunkRight)

            // Get start and end times
            val firstUbt = ubtList[chunkLeft]
            val lastUbt = ubtList[chunkRight - 1]

            val strStartTime = firstUbt.startTime
            val strEndTime = lastUbt.startTime

            // Create a unique ID that's more deterministic and less random
            val summaryId = generateSummaryId(uid, strStartTime, strEndTime)

            // Clear MDC
            clearMDCInfo()

            // Create and return a summary object
            SummaryL3DTO(
                summaryId = summaryId,
                uid = uid,
                startTime = strStartTime,
                endTime = strEndTime,
                endDate = DateUtils.getDateString(lastUbt.startTimeMills),
                topicId = topicId,
                topic = topic,
                summary = escapeSpecialCharacters(summary),
                isNew = true,
                level = "L3",
                updateTime = DateUtils.now(),
            )
        } catch (e: Exception) {
            logger.error("Error generating summary for chunk [$chunkLeft, $chunkRight): ${e.message}", e)
            null
        }
    }

    /**
     * Clears the MDC context.
     */
    private fun clearMDCInfo() {
        MDC.clear()
    }

    /**
     * Add some key information to the MDC
     */
    private fun addMdcInfo(chunkUBTs: List<DetailL2DTO>) {
        MDC.put("start_time", chunkUBTs.first().startTime)
        MDC.put("end_time", chunkUBTs.last().startTime)
    }

    private fun addRequestInfoToMDC(uid: String, callTime: String, topicId: String, requestId: String) {
        MDC.put("uid", uid)
        MDC.put("request_id", requestId)
        MDC.put("call_time", callTime)
        MDC.put("topic_id", topicId)
    }

    /**
     * Generates a unique ID for the summary.
     */
    private fun generateSummaryId(uid: String?, startTime: String?, endTime: String?): String {
        // Create a more deterministic ID based on the data instead of random numbers
        val baseData = "$uid$startTime$endTime"
        val hashCode = baseData.hashCode() and 0x7fffffff // Ensure a positive number
        val formattedHash = "%06d".format(hashCode % 1000000)
        return "$startTime-$endTime#$formattedHash"
    }

    /**
     * Escapes special characters in the summary text.
     */
    private fun escapeSpecialCharacters(text: String): String {
        return text.replace("\n", "\\n").replace("\"", "\\\"")
    }

    private fun generatePrompt(
        topic: String,
        chunkUBTs: List<DetailL2DTO?>
    ): List<Map<String, String>> {
        val prompts = mutableListOf<Map<String, String>>()
        val promptProperties = TypedConfig.get("prompt.yml", PromptParse())
        val properties = promptProperties.current()

        // Add a system message with instructions
        createSystemMessage(prompts, properties)

        // Add a user message with a topic and logs
        val ubtDetailList = chunkUBTs.map { ubt ->
            Pair(ubt?.startTime, ubt?.value)
        }

        val content = MessageFormat.format(
            properties.user ?: "",
            topic,
            cleanJsonForPrompt(JsonUtil.toJson(ubtDetailList))
        )

        prompts.add(mapOf("user" to content))

        // Log detailed prompt information
        prompts.forEachIndexed { index, prompt ->
            prompt.forEach { (role, promptContent) ->
                logger.info("Prompt #$index - Role: $role, Content:$promptContent")
            }
        }

        return prompts
    }

    private fun createSystemMessage(prompts: MutableList<Map<String, String>>, properties: PromptConfig) {
        prompts.add(mapOf("system" to (properties.system ?: "")))
    }

    private fun saveCostTime(
        uid: String,
        requestId: String,
        toJson: String?,
        summary: String,
        lng: Long,
        chunkLeft: Int,
        chunkRight: Int
    ) {
        userProfileCostTimeService.saveCostTime(
            UserProfileTimeCost(
                uid = uid,
                requestId = requestId,
                costType = Constant.L3_SUMMARY_COST_TYPE + "_" + chunkLeft + "_" + chunkRight,
                costTime = lng,
                input = toJson,
                output = summary
            )
        )
    }

}