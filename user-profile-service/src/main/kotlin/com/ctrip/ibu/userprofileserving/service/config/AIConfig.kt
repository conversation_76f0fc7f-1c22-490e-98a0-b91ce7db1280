package com.ctrip.ibu.userprofileserving.service.config

class AIConfig {
    val apikey: String? = null
    val baseurl: String? = null
    val model: String? = null
    val temperature: Double? = null
    val maxTokens: Int? = null
    val isMockResp: Boolean = false
    val geminiProject: String? = null
    val geminiLocation: String? = null
    val geminiModel: String? = null
    val isThinking: Boolean = false
    val kmsAppId: String? = null
    val kmsToken: String? = null
    val proxyHost: String? = null
    val proxyPort: Int = 0
}


class PromptConfig {
    var system: String? = null
    var user: String? = null
}

class UserPromptConfig {
    var user: String? = null
}

class CmLLMConfig{
    val token: String = ""
    val appId: Long = 0
    val projectId: Long = 0
    val timeout: Long = 10000
    val maxRetries: Int = 3
}