package com.ctrip.ibu.userprofileserving.service.config

data class CommonConfig(
    val attractionSlb: String = "",
    val workflowAllTimeoutMs: Long = 0,
    val l3AgentTimeoutMs: Long = 0,
    val cmtDescriptionAgentTimeoutMs: Long = 0,
    val productSelAgentTimeoutMs: Long = 0,
    val attractionAgentTimeoutMs: Long = 0,
    val reasonAgentTimeoutMs: Long = 0,
    val hotelFilterAgentTimeoutMs: Long = 0,
    val hotelSearchToken: String = "",
    val mockL3Uid: String? = null,
    val allowFilterType: String? = null,
    val maxCharEN: Int = 0,
    val maxCharZH: Int = 0,
    val maxCharJA: Int = 0,
    val maxCharKO: Int = 0,
    val maxCharTH: Int = 0,
    val minChar: Int = 0,
    val cdpMemberGradeTagId: String = "",
    val cdpAgeGroupTagId: String = "",
    val cdpHotelSuccessOrdersTagId: String = "",
    val cdpHotelPriceSensitivityTagId: String = "",
    val cdpHotelConsumptionCapacityTagId: String = "",
    val cdpBusinessTravelerTagId: String = "",
    val cdpFamilyTypeTagId: String = "",
    val cdpFamilyWithMultipleChildrenTagId: String = "",
    val isOBKVRead: Boolean = false,
    val isOBKVWrite: Boolean = false,
    val isMysqlRead: Boolean = false,
    val isMysqlWrite: Boolean = false,
)