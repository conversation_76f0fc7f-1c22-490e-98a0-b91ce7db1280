package com.ctrip.ibu.userprofileserving.service.config

import com.ctrip.arch.distlock.DistributedLockService
import com.ctrip.arch.distlock.redis.RedisDistributedLockService
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import java.util.concurrent.TimeUnit


@Configuration
class DistributedLockConfig {
    @Bean
    fun distributedLockService(): DistributedLockService {
        // The default expiration time is 30 seconds.
        // After 30 seconds, the lock will not be released;
        // it will only refresh the expiration time,
        // so an unlock must be performed.
        return RedisDistributedLockService("ibu.userprofile",360, TimeUnit.SECONDS)
    }
}