package com.ctrip.ibu.userprofileserving.service.config

import com.ctrip.ops.hickwall.HickwallUDPReporter
import io.dropwizard.metrics5.MetricRegistry
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import java.util.concurrent.TimeUnit

@Configuration
class MetricConfig {

    @Bean
    fun metric(): MetricRegistry {
        val metrics = MetricRegistry()
        HickwallUDPReporter.enable(
            metrics,
            60,
            TimeUnit.SECONDS,
            "IBU"
        )
        return metrics
    }
}