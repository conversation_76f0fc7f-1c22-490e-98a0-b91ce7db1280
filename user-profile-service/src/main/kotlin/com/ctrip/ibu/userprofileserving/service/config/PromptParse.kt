package com.ctrip.ibu.userprofileserving.service.config

import qunar.tc.qconfig.client.TypedConfig

class PromptParse : TypedConfig.Parser<PromptConfig> {
    override fun parse(data: String?): PromptConfig? {
        return PromptConfig().apply {
            data?.split("(?m)^\\s*(?=system:|user:)".toRegex())
                ?.filterNot { it.isEmpty() }
                ?.forEach { part ->
                    when {
                        part.startsWith("system:") ->
                            system = part.replaceFirst("system:\\s*>?\\s*".toRegex(), "").trim()

                        part.startsWith("user:") ->
                            user = part.replaceFirst("user:\\s*>?\\s*".toRegex(), "").trim()
                    }
                }
        }
    }
}