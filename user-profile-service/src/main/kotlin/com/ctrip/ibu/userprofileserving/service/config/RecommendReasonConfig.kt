package com.ctrip.ibu.userprofileserving.service.config

import com.fasterxml.jackson.annotation.JsonProperty

class RecommendReasonConfig {
    @JsonProperty("default-recommend-reason")
    var defaultRecommendReason: Map<String, LocaleRecommendReason> = emptyMap()
}

data class LocaleRecommendReason(
    val language: String = "",
    @JsonProperty("recommend_text")
    val recommendText: Map<String, List<String>> = emptyMap()
)