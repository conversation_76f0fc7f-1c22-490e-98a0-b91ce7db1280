package com.ctrip.ibu.userprofileserving.service.config

import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import org.slf4j.LoggerFactory
import qunar.tc.qconfig.client.TypedConfig

class RecommendReasonParse: TypedConfig.Parser<RecommendReasonConfig> {

    private val log = LoggerFactory.getLogger(RecommendReasonParse::class.java)

    private val yamlMapper = ObjectMapper(YAMLFactory()).apply {
        registerKotlinModule()
        configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
        configure(DeserializationFeature.FAIL_ON_NULL_FOR_PRIMITIVES, false)
        configure(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT, true)
    }

    override fun parse(data: String): RecommendReasonConfig? {
        return try {
            if (data.isBlank()) {
                log.warn("Empty YAML data provided for RecommendReasonConfig")
                return RecommendReasonConfig()
            }

            yamlMapper.readValue(data, RecommendReasonConfig::class.java)
        } catch (e: Exception) {
            log.error("Failed to parse YAML data for RecommendReasonConfig: $data", e)
            null
        }
    }
}