package com.ctrip.ibu.userprofileserving.service.config

import qunar.tc.qconfig.client.TypedConfig

class UserPromptParse : TypedConfig.Parser<UserPromptConfig> {
    override fun parse(data: String?): UserPromptConfig? {
        return UserPromptConfig().apply {
            data?.split("(?m)^\\s*(?=user:)".toRegex())
                ?.filterNot { it.isEmpty() }
                ?.forEach { part ->
                    when {
                        part.startsWith("user:") ->
                            user = part.replaceFirst("user:\\s*>?\\s*".toRegex(), "").trim()
                    }
                }
        }
    }
}