package com.ctrip.ibu.userprofileserving.service.dao.entity.mysql

import org.ktorm.database.Database
import org.ktorm.entity.Entity
import org.ktorm.entity.sequenceOf
import org.ktorm.schema.Table
import org.ktorm.schema.long
import org.ktorm.schema.timestamp
import org.ktorm.schema.varchar
import java.time.Instant

interface UserProfileRecommend : Entity<UserProfileRecommend> {
    companion object : Entity.Factory<UserProfileRecommend>()

    val id: Long
    var uid: String
    var cacheKey: String
    var requestId: String
    var product: String
    var value: String?
    var status: String?
    var createdAt: Instant?
    val dataChangeLastTime: Instant?
}


object UserProfileRecommends : Table<UserProfileRecommend>("ibu_user_profile_recommend") {
    val id = long("id").primaryKey().bindTo { it.id }
    val uid = varchar("uid").bindTo { it.uid }
    val cacheKey = varchar("cache_key").bindTo { it.cacheKey }
    val requestId = varchar("request_id").bindTo { it.requestId }
    val product = varchar("product").bindTo { it.product }
    val value = varchar("value").bindTo { it.value }
    val status = varchar("status").bindTo { it.status }
    val createdAt = timestamp("created_at").bindTo { it.createdAt }
    val dataChangeLastTime = timestamp("datachange_lasttime").bindTo { it.dataChangeLastTime }
}

val Database.UserProfileRecommend get() = this.sequenceOf(UserProfileRecommends)