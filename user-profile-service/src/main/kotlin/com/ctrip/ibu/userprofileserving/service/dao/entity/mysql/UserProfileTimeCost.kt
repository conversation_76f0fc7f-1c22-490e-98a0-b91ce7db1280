package com.ctrip.ibu.userprofileserving.service.dao.entity.mysql

import org.ktorm.database.Database
import org.ktorm.entity.Entity
import org.ktorm.entity.sequenceOf
import org.ktorm.schema.Table
import org.ktorm.schema.long
import org.ktorm.schema.timestamp
import org.ktorm.schema.varchar
import java.time.Instant

interface UserProfileTimeCost : Entity<UserProfileTimeCost> {
    companion object : Entity.Factory<UserProfileTimeCost>()

    val id: Long
    var uid: String
    var product: String
    var requestId: String
    var costType: String
    var costTime: Long
    var input: String?
    var output: String?
    var createdTime: Instant?
    var dataChangeLastTime: Instant?
}


object UserProfileTimeCosts : Table<UserProfileTimeCost>("ibu_user_profile_time_cost") {
    val id = long("id").primaryKey().bindTo { it.id }
    val uid = varchar("uid").bindTo { it.uid }
    val product = varchar("product").bindTo { it.product }
    val requestId = varchar("request_id").bindTo { it.requestId }
    val costType = varchar("cost_type").bindTo { it.costType }
    val costTime = long("cost_time").bindTo { it.costTime }
    val input = varchar("input").bindTo { it.input }
    val output = varchar("output").bindTo { it.output }
    val createdTime = timestamp("created_at").bindTo { it.createdTime }
    val dataChangeLastTime = timestamp("datachange_lasttime").bindTo { it.dataChangeLastTime }
}

val Database.UserProfileTimeCost get() = this.sequenceOf(UserProfileTimeCosts)