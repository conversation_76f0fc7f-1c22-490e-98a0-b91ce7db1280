package com.ctrip.ibu.userprofileserving.service.dao.mysql

import com.ctrip.ibu.userprofileserving.service.dao.entity.mysql.UserProfileRecommend
import com.ctrip.ibu.userprofileserving.service.dao.entity.mysql.UserProfileRecommends
import com.ctrip.ibu.userprofileserving.service.dao.entity.obkv.UserProfileCMTRecommend
import org.ktorm.dsl.and
import org.ktorm.dsl.eq
import org.ktorm.support.mysql.insertOrUpdate
import org.springframework.stereotype.Component

@Component
class UserProfileRecommendMysqlDao : BaseDao<UserProfileRecommend, UserProfileRecommends>(UserProfileRecommends) {
    fun getUserProfileRecommend(uid: String, itineraryKey: String): List<UserProfileCMTRecommend> {
        return findList {
            it.uid eq uid
            it.cacheKey eq itineraryKey
        }.map { userProfileRecommend ->
            UserProfileCMTRecommend(
                uid = userProfileRecommend.uid,
                itineraryKey = userProfileRecommend.cacheKey,
                requestId = userProfileRecommend.requestId,
                value = userProfileRecommend.value,
                status = userProfileRecommend.status
            )
        }
    }

    fun getUserProfileRecommendByRequestId(uid: String, requestId: String): UserProfileCMTRecommend? {
        return findOne {
            it.uid eq uid
            it.requestId eq requestId
        }?.let { userProfileRecommend ->
            UserProfileCMTRecommend(
                uid = userProfileRecommend.uid,
                itineraryKey = userProfileRecommend.cacheKey,
                requestId = userProfileRecommend.requestId,
                value = userProfileRecommend.value,
                status = userProfileRecommend.status
            )
        }
    }

    fun delete(uid: String, itineraryKey: String, requestId: String) {
        deleteIf {
            it.uid eq uid and (it.cacheKey eq itineraryKey) and (it.requestId eq requestId)
        }
    }

    fun saveUserProfileRecommend(userProfileRecommend: UserProfileRecommend) {
        this.database.insertOrUpdate(UserProfileRecommends) {
            set(it.uid, userProfileRecommend.uid)
            set(it.cacheKey, userProfileRecommend.cacheKey)
            set(it.requestId, userProfileRecommend.requestId)
            set(it.product, userProfileRecommend.product)
            set(it.value, userProfileRecommend.value)
            set(it.status, userProfileRecommend.status)
            set(it.createdAt, userProfileRecommend.createdAt)
            onDuplicateKey {
                set(it.value, userProfileRecommend.value)
                set(it.status, userProfileRecommend.status)
            }
        }
    }
}