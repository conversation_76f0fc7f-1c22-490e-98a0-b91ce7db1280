package com.ctrip.ibu.userprofileserving.service.dao.mysql

import com.ctrip.ibu.userprofileserving.service.dao.entity.mysql.UserProfileTimeCost
import com.ctrip.ibu.userprofileserving.service.dao.entity.mysql.UserProfileTimeCosts
import com.ctrip.ibu.userprofileserving.service.enums.UserProfileProductType
import org.ktorm.dsl.and
import org.ktorm.dsl.eq
import org.ktorm.support.mysql.insertOrUpdate
import org.springframework.stereotype.Component

@Component
class UserProfileTimeCostMysqlDao : BaseDao<UserProfileTimeCost, UserProfileTimeCosts>(UserProfileTimeCosts) {

    fun getTimeCosts(
        uid: String,
        requestId: String,
        product: String = UserProfileProductType.CMT.value
    ): List<UserProfileTimeCost> {
        return findList {
            it.uid eq uid
            it.product eq product
            it.requestId eq requestId
        }
    }

    fun delete(uid: String, requestId: String) {
        deleteIf {
            it.uid eq uid and (it.requestId eq requestId)
        }
    }

    fun saveUserProfileTimeCost(userProfileTimeCost: UserProfileTimeCost) {
        this.database.insertOrUpdate(UserProfileTimeCosts, {
            set(it.uid, userProfileTimeCost.uid)
            set(it.requestId, userProfileTimeCost.requestId)
            set(it.product, userProfileTimeCost.product)
            set(it.costTime, userProfileTimeCost.costTime)
            set(it.costType, userProfileTimeCost.costType)
            set(it.input, userProfileTimeCost.input)
            set(it.output, userProfileTimeCost.output)
            set(it.createdTime, userProfileTimeCost.createdTime)
            onDuplicateKey {
                set(it.input, userProfileTimeCost.input)
                set(it.output, userProfileTimeCost.output)
            }
        })
    }
}