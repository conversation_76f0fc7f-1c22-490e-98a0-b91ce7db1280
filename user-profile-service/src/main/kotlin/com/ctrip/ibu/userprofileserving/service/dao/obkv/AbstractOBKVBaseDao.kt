package com.ctrip.ibu.userprofileserving.service.dao.obkv

import com.ctrip.framework.kv.obkv.OBKVClientFactory
import com.ctrip.framework.kv.obkv.TableKVClient
import com.ctrip.framework.kv.obkv.TableMeta

abstract class AbstractOBKVBaseDao(
    dbName: String,
    tableName: String,
    rowKeyNames: Array<String>,
    valueColumnNames: Array<String>,
    defaultReadValueColumnName: String,
    defaultCompareValueColumnName: String
) {
    val tableKVClient: TableKVClient = OBKVClientFactory.getObKVClient(
        dbName,
        tableName,
        TableMeta.builder()
            .setRowKeyNames(rowKeyNames)
            .setValueColumnNames(valueColumnNames)
            .setDefaultReadValueColumnName(defaultReadValueColumnName)
            .setDefaultCompareValueColumnName(defaultCompareValueColumnName)
            .build()
    )
}