package com.ctrip.ibu.userprofileserving.service.dao.obkv

import com.ctrip.framework.kv.obkv.api.KvTableFilterValueFactory
import com.ctrip.framework.kv.obkv.api.KvTableScanQuery
import com.ctrip.ibu.userprofileserving.service.dao.entity.obkv.UserProfileCMTRecommend
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Repository
import java.util.Date
import kotlin.collections.iterator

@Repository
class UserProfileCMTRecommendDao : AbstractOBKVBaseDao(
    dbName = "ibuuserprofiledb",
    tableName = "ibu_user_profile_cmt_recommend",
    rowKeyNames = arrayOf("uid", "itinerary_key", "request_id"),
    valueColumnNames = arrayOf("uid", "itinerary_key", "request_id", "value", "status"),
    defaultReadValueColumnName = "uid",
    defaultCompareValueColumnName = "uid"
) {

    companion object {
        private val logger = LoggerFactory.getLogger(UserProfileCMTRecommendDao::class.java)
    }


    fun insert(userProfileCMTRecommend: UserProfileCMTRecommend) {
        val rowKey = arrayOf<Any>(
            userProfileCMTRecommend.uid,
            userProfileCMTRecommend.itineraryKey,
            userProfileCMTRecommend.requestId
        )
        tableKVClient.put(
            rowKey,
            userProfileCMTRecommend.uid,
            userProfileCMTRecommend.itineraryKey,
            userProfileCMTRecommend.requestId,
            userProfileCMTRecommend.value,
            userProfileCMTRecommend.status
        )
    }

    fun delete(uid: String, itineraryKey: String, requestId: String) {
        val rowKey = arrayOf<Any>(uid, itineraryKey, requestId)
        tableKVClient.delete(rowKey)
    }


    fun selectUserProfileCMTRecommend(uid: String, itineraryKey: String): List<UserProfileCMTRecommend> {
        val rowStart = arrayOf<Any>(uid, itineraryKey)
        val rowEnd = arrayOf<Any>(uid, itineraryKey)
        val query = KvTableScanQuery.builder()
            .addScanRange(rowStart, rowEnd)
            .setScanRangeColumns("uid", "itinerary_key")
            .select("uid", "itinerary_key", "request_id", "value", "status")
            .build()
        val result = tableKVClient.scan(query)
        return parseUserProfileCMTRecommend(result)
    }

    fun selectUserProfileCMTRecommendByRequestId(uid: String, requestId: String): UserProfileCMTRecommend? {
        val rowStart = arrayOf<Any>(uid)
        val rowEnd = arrayOf<Any>(uid)
        val filter = KvTableFilterValueFactory.`in`("request_id", requestId)
        val query = KvTableScanQuery.builder()
            .setFilter(filter)
            .addScanRange(rowStart, rowEnd)
            .setScanRangeColumns("uid")
            .select("uid", "itinerary_key", "request_id", "value", "status")
            .build()
        val result = tableKVClient.scan(query)
        return parseUserProfileCMTRecommend(result).firstOrNull()
    }

    private fun parseUserProfileCMTRecommend(result: Iterator<Map<String, Any>>): List<UserProfileCMTRecommend> {
        return sequence {
            for (map in result) {
                try {
                    val uid = map["uid"] as String
                    val itineraryKey = map["itinerary_key"] as String
                    val requestId = map["request_id"] as String
                    val value = map["value"] as? String
                    val status = map["status"] as? String
                    val dataChangeLastTime = map["datachange_lasttime"] as? Date
                    yield(
                        UserProfileCMTRecommend(
                            uid = uid,
                            itineraryKey = itineraryKey,
                            requestId = requestId,
                            value = value,
                            status = status,
                            dataChangeLastTime = dataChangeLastTime
                        )
                    )
                } catch (e: Exception) {
                    logger.error("Failed to parse UserProfileCMTRecommend: {},{}", map, e)
                }
            }
        }.toList()
    }

    fun getAllUserProfileCMTRecommends(uids: List<String>): List<UserProfileCMTRecommend> {
        if (uids.isEmpty()) {
            return emptyList()
        }
        return uids.flatMap { uid ->
            val rowStart = arrayOf<Any>(uid)
            val rowEnd = arrayOf<Any>(uid)
            val query = KvTableScanQuery.builder()
                .addScanRange(rowStart, rowEnd)
                .setScanRangeColumns("uid")
                .select("uid", "itinerary_key", "request_id", "value", "status", "datachange_lasttime")
                .build()
            val result = tableKVClient.scan(query)
            parseUserProfileCMTRecommend(result)
        }
    }
}