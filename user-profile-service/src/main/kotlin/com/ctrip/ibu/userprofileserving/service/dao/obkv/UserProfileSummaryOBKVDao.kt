package com.ctrip.ibu.userprofileserving.service.dao.obkv

import com.alibaba.fastjson.JSON
import com.ctrip.framework.kv.obkv.api.KvTableScanQuery
import com.ctrip.ibu.userprofileserving.service.dao.entity.obkv.UserProfileSummary
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Repository

@Repository
class UserProfileSummaryOBKVDao : AbstractOBKVBaseDao(
    dbName = "ibuuserprofiledb",
    tableName = "ibu_user_profile_summary",
    rowKeyNames = arrayOf("uid", "time", "topic_id"),
    valueColumnNames = arrayOf("uid", "time", "topic_id", "value"),
    defaultReadValueColumnName = "value",
    defaultCompareValueColumnName = "value"
) {

    companion object {
        val logger: Logger = LoggerFactory.getLogger(UserProfileSummaryOBKVDao::class.java)
    }

    fun insert(userProfileSummary: UserProfileSummary): Long {
        val rowKey = arrayOf<Any>(
            userProfileSummary.uid, userProfileSummary.endTime, userProfileSummary.topicId
        )
        return tableKVClient.put(
            rowKey,
            userProfileSummary.uid,
            userProfileSummary.endTime,
            userProfileSummary.topicId,
            userProfileSummary.value
        )
    }

    fun batchInsert(userProfileSummaries: List<UserProfileSummary>) {
        if (userProfileSummaries.isEmpty()) {
            return
        }

        for (userProfileSummary in userProfileSummaries) {
            val rowKey = arrayOf<Any>(
                userProfileSummary.uid, userProfileSummary.endTime, userProfileSummary.topicId
            )
            tableKVClient.put(
                rowKey,
                userProfileSummary.uid,
                userProfileSummary.endTime,
                userProfileSummary.topicId,
                userProfileSummary.value
            )
        }
    }


    fun deleteSummariesInRange(uid: String, startTime: Long, endTime: Long, topicId: String) {
        val scanStartKey: Array<Any> = arrayOf(uid, startTime, topicId)
        val scanEndKey: Array<Any> = arrayOf(uid, endTime, topicId)

        val scanQuery = KvTableScanQuery.builder()
            .addScanRange(scanStartKey, scanEndKey)
            .select("uid", "time", "topic_id")
            .build()

        val summariesFound: List<UserProfileSummary> = processUserProfileSummaryRowKeys(tableKVClient.scan(scanQuery))
        summariesFound.takeIf { it.isNotEmpty() }?.let { summaries ->
            val keysToDelete: List<Array<Any>> = summaries.map { summary ->
                arrayOf(summary.uid, summary.startTime, summary.topicId)
            }
            tableKVClient.batchDelete(keysToDelete)
        }
    }

    fun getUserProfileSummary(
        uid: String, startTime: Long, endTime: Long, topicId: String
    ): List<UserProfileSummary> {
        val rowStart = arrayOf<Any>(uid, startTime, topicId)
        val rowEnd = arrayOf<Any>(uid, endTime, topicId)

        val query = KvTableScanQuery.builder().addScanRange(rowStart, rowEnd).select("uid", "time", "value", "topic_id").build()

        return processUserProfileSummary(tableKVClient.scan(query))
    }


    private fun processUserProfileSummary(iterator: MutableIterator<MutableMap<String, Any>>): List<UserProfileSummary> {
        return iterator.asSequence()
            .mapNotNull { map ->
                try {
                    val uid = map["uid"] as String
                    val endTime = map["time"] as Long
                    val topicId = map["topic_id"] as String
                    val valueString = map["value"] as String
                    val jsonObject = JSON.parseObject(valueString)
                    val startTime = jsonObject.getLongValue("time")
                    UserProfileSummary(
                        uid = uid,
                        startTime = startTime,
                        endTime = endTime,
                        topicId = topicId,
                        value = valueString
                    )
                } catch (e: Exception) {
                    logger.error("Failed to parse UserProfileSummary: {},{}", map, e)
                    null
                }
            }
            .toList()
    }

    private fun processUserProfileSummaryRowKeys(iterator: MutableIterator<MutableMap<String, Any>>): List<UserProfileSummary> {
        return iterator.asSequence().mapNotNull { map ->
            try {
                val uid = map["uid"] as String
                val time = map["time"] as Long
                val topicId = map["topic_id"] as String
                UserProfileSummary(
                    uid = uid, startTime = time, endTime = time, topicId = topicId, value = null
                )
            } catch (e: Exception) {
                logger.error("Failed to parse UserProfileSummary to RowKeys: {},{}", map, e)
                null
            }
        }.toList()
    }
}