package com.ctrip.ibu.userprofileserving.service.dao.obkv

import com.ctrip.framework.kv.obkv.api.KvTableScanQuery
import com.ctrip.ibu.userprofileserving.service.dao.entity.obkv.UserProfileTimeCost
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Repository
import java.util.Date
import kotlin.collections.iterator

@Repository
class UserProfileTimeCostOBKVDao : AbstractOBKVBaseDao(
    dbName = "ibuuserprofiledb",
    tableName = "ibu_user_profile_time_cost",
    rowKeyNames = arrayOf("uid", "request_id", "cost_type"),
    valueColumnNames = arrayOf("uid", "request_id", "cost_type", "cost_time", "input", "output"),
    defaultReadValueColumnName = "uid",
    defaultCompareValueColumnName = "uid"
) {

    companion object {
        private val logger = LoggerFactory.getLogger(UserProfileTimeCostOBKVDao::class.java)
    }

    fun insert(userProfileTimeCost: UserProfileTimeCost) {
        val rowKey = arrayOf<Any>(
            userProfileTimeCost.uid,
            userProfileTimeCost.requestId,
            userProfileTimeCost.costType
        )
        tableKVClient.put(
            rowKey,
            userProfileTimeCost.uid,
            userProfileTimeCost.requestId,
            userProfileTimeCost.costType,
            userProfileTimeCost.costTime,
            userProfileTimeCost.input,
            userProfileTimeCost.output
        )
    }

    fun delete(uid: String, requestId: String) {
        val startRowKey = arrayOf<Any>(uid, requestId)
        val endRowKey = arrayOf<Any>(uid, requestId)
        val query = KvTableScanQuery.builder()
            .addScanRange(startRowKey, endRowKey)
            .setScanRangeColumns("uid", "request_id")
            .select("uid", "request_id", "cost_type")
            .build()
        val result = tableKVClient.scan(query)
        for (map in result) {
            val rowKey = arrayOf<Any>(
                map["uid"] as String,
                map["request_id"] as String,
                map["cost_type"] as String
            )
            tableKVClient.delete(rowKey)
        }
    }

    fun getUserProfileTimeCost(uid: String, requestId: String): List<UserProfileTimeCost> {
        val rowStart = arrayOf<Any>(uid, requestId)
        val rowEnd = arrayOf<Any>(uid, requestId)
        val query = KvTableScanQuery.builder()
            .addScanRange(rowStart, rowEnd)
            .setScanRangeColumns("uid", "request_id")
            .select("uid", "request_id", "cost_type", "cost_time", "input", "output", "datachange_lasttime")
            .build()
        return parseUserProfileTimeCost(tableKVClient.scan(query))
    }

    private fun parseUserProfileTimeCost(result: Iterator<Map<String, Any>>): List<UserProfileTimeCost> {
        return sequence {
            for (map in result) {
                try {
                    val uid = map["uid"] as String
                    val requestId = map["request_id"] as String
                    val costType = map["cost_type"] as String
                    val costTime = map["cost_time"] as Long
                    val input = map["input"] as? String
                    val output = map["output"] as? String
                    val dataChangeLastTime = map["datachange_lasttime"] as? Date
                    yield(UserProfileTimeCost(
                        uid = uid,
                        requestId = requestId,
                        costType = costType,
                        costTime = costTime,
                        input = input,
                        output = output,
                        dataChangeLastTime = dataChangeLastTime
                    ))
                } catch (e: Exception) {
                    logger.error("Failed to parse UserProfileTimeCost: {},{}", map, e)
                }
            }
        }.toList()
    }
}