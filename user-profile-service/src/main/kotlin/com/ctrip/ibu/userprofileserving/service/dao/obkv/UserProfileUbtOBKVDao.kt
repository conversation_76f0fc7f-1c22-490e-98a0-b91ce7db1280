package com.ctrip.ibu.userprofileserving.service.dao.obkv

import com.ctrip.framework.kv.obkv.api.KvTableScanQuery
import com.ctrip.ibu.userprofileserving.service.dao.entity.obkv.UserProfileUbt
import com.ctrip.ibu.userprofileserving.service.dto.DetailL2DTO
import com.ctrip.ibu.userprofileserving.service.enums.UbtDataType
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Repository

@Repository
class UserProfileUbtOBKVDao : AbstractOBKVBaseDao(
    dbName = "ibuuserprofiledb",
    tableName = "ibu_user_profile_ubt",
    rowKeyNames = arrayOf("uid", "start_time", "event_id"),
    valueColumnNames = arrayOf("uid", "start_time", "event_id", "value"),
    defaultReadValueColumnName = "start_time",
    defaultCompareValueColumnName = "start_time"
) {

    companion object {
        private val logger = LoggerFactory.getLogger(UserProfileUbtOBKVDao::class.java)
    }

    fun insert(userProfileUbt: UserProfileUbt) {
        val rowKey = arrayOf<Any>(userProfileUbt.uid, userProfileUbt.startTime, userProfileUbt.eventId)
        tableKVClient.put(
            rowKey,
            userProfileUbt.uid,
            userProfileUbt.startTime,
            userProfileUbt.eventId,
            userProfileUbt.value
        )
    }

    fun getUserProfileUbt(uid: String, startTime: Long, endTime: Long): List<UserProfileUbt> {

        val rowStart = arrayOf<Any>(uid, startTime)
        val rowEnd = arrayOf<Any>(uid, endTime)

        val query = KvTableScanQuery.builder()
            .addScanRange(rowStart, rowEnd)
            .setScanRangeColumns("uid", "start_time")
            .select("uid", "start_time", "event_id")
            .build()

        return parseUserProfileUbts(tableKVClient.scan(query))
    }

    fun getUbtByRowKeys(detailL2DTOList: List<DetailL2DTO>): List<UserProfileUbt> {
        if (detailL2DTOList.isEmpty()) {
            return emptyList()
        }

        val rowKeys = detailL2DTOList
            .filter { it.type == UbtDataType.CUSTOM_EVENT.type }
            .map { arrayOf(it.uid as Any, it.startTimeMills as Any, it.uniqueId as Any) }

        if (rowKeys.isEmpty()) {
            return emptyList()
        }

        val list = tableKVClient.batchGet(rowKeys, listOf("uid", "start_time", "value"))
        return parseUserProfileUbts(list.iterator())
    }

    private fun parseUserProfileUbts(mapIterator: MutableIterator<MutableMap<String, Any>>): List<UserProfileUbt> {
        return mapIterator.asSequence()
            .mapNotNull { map ->
                try {
                    val uid = map["uid"] as String
                    val startTime = map["start_time"] as Long
                    val rawValue = map["value"] as? String ?: ""
                    val value = rawValue
                    val eventId = map["event_id"] as? String ?: ""
                    UserProfileUbt(
                        uid = uid,
                        startTime = startTime,
                        value = value,
                        eventId = eventId
                    )
                } catch (e: Exception) {
                    logger.error(
                        "Failed to construct UserProfileUbt for from map: {},{}",
                        map,
                        e
                    )
                    null
                }
            }
            .toList()
    }
}







