package com.ctrip.ibu.userprofileserving.service.dao.obkv

import com.ctrip.framework.kv.obkv.api.KvTableScanQuery
import com.ctrip.ibu.userprofileserving.service.dao.entity.obkv.UserProfileUbtOrderCompress
import com.ctrip.ibu.userprofileserving.service.dto.DetailL2DTO
import com.ctrip.ibu.userprofileserving.service.enums.UbtDataType
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Repository

@Repository
class UserProfileUbtOrderCompressOBKVDao : AbstractOBKVBaseDao(
    dbName = "ibuuserprofiledb",
    tableName = "ibu_user_profile_order_compress",
    rowKeyNames = arrayOf("uid", "order_id"),
    valueColumnNames = arrayOf("uid", "order_id","start_time", "value"),
    defaultReadValueColumnName = "start_time",
    defaultCompareValueColumnName = "start_time"
) {

    companion object {
        private val logger = LoggerFactory.getLogger(UserProfileUbtOrderCompressOBKVDao::class.java)
    }

    fun getUserProfileUbtOrderCompress(uid: String): List<UserProfileUbtOrderCompress> {
        val rowStart = arrayOf<Any>(uid)
        val rowEnd = arrayOf<Any>(uid)

        val query = KvTableScanQuery.builder()
            .addScanRange(rowStart, rowEnd)
            .setScanRangeColumns("uid")
            .select(
                "uid",
                "start_time",
                "order_id"
            )
            .build()
        return parseUserProfileUbtOrders(tableKVClient.scan(query))
    }

    fun getUbtOrderCompressByRowKeys(detailL2DTOList: List<DetailL2DTO>): List<UserProfileUbtOrderCompress> {
        if (detailL2DTOList.isEmpty()) {
            return emptyList()
        }

        val rowKeys = detailL2DTOList
            .filter { it.type == UbtDataType.ORDER_INDEX.type }
            .map {
                arrayOf(
                    it.uid as Any,
                    it.uniqueId as Any
                )
            }

        if (rowKeys.isEmpty()) {
            return emptyList()
        }

        val list = tableKVClient.batchGet(rowKeys, listOf("uid", "order_id", "start_time", "value"))
        return parseUserProfileUbtOrders(list.iterator())
    }

    private fun parseUserProfileUbtOrders(mapIterator: MutableIterator<MutableMap<String, Any>>): List<UserProfileUbtOrderCompress> {
        return mapIterator.asSequence()
            .mapNotNull { map ->
                try {
                    val uid = map["uid"] as String
                    val startTime = map["start_time"] as Long
                    val rawValue = map["value"] as? String ?: ""
//                    val value = CommonUtil.processJsonValue(
//                        rawValue,
//                        emptyList(),
//                        reduceTokenConfig.removeSpecialChars
//                    )
                    val value = rawValue
                    val orderId = map["order_id"] as? String ?: ""
                    UserProfileUbtOrderCompress(
                        uid = uid,
                        startTime = startTime,
                        value = value,
                        orderId = orderId
                    )
                } catch (e: Exception) {
                    logger.error(
                        "Failed to construct UserProfileUbtOrder for from map: {},{}",
                        map,
                        e
                    )
                    null
                }
            }
            .toList()
    }
}