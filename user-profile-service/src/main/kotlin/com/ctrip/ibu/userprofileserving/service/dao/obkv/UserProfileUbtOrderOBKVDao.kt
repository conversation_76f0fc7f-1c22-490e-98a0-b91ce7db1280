package com.ctrip.ibu.userprofileserving.service.dao.obkv

import com.ctrip.framework.kv.obkv.api.KvTableScanQuery
import com.ctrip.ibu.userprofileserving.service.dao.entity.obkv.UserProfileUbtOrder
import com.ctrip.ibu.userprofileserving.service.dto.DetailL2DTO
import com.ctrip.ibu.userprofileserving.service.enums.UbtDataType
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Repository

@Repository
class UserProfileUbtOrderOBKVDao : AbstractOBKVBaseDao(
    dbName = "ibuuserprofiledb",
    tableName = "ibu_user_profile_ubt_order",
    rowKeyNames = arrayOf("uid", "start_time", "order_id"),
    valueColumnNames = arrayOf("uid", "start_time", "order_id", "value"),
    defaultReadValueColumnName = "start_time",
    defaultCompareValueColumnName = "start_time"
) {

    companion object {
        private val logger = LoggerFactory.getLogger(UserProfileUbtOrderOBKVDao::class.java)
    }

    fun insert(userProfileUbtOrder: UserProfileUbtOrder) {
        val rowKey = arrayOf<Any>(
            userProfileUbtOrder.uid,
            userProfileUbtOrder.startTime,
            userProfileUbtOrder.orderId
        )
        tableKVClient.put(
            rowKey,
            userProfileUbtOrder.uid,
            userProfileUbtOrder.startTime,
            userProfileUbtOrder.orderId,
            userProfileUbtOrder.value
        )
    }

    fun getUserProfileUbtOrder(uid: String, startTime: Long, endTime: Long): List<UserProfileUbtOrder> {
        val rowStart = arrayOf<Any>(uid, startTime)
        val rowEnd = arrayOf<Any>(uid, endTime)

        val query = KvTableScanQuery.builder()
            .addScanRange(rowStart, rowEnd)
            .setScanRangeColumns("uid", "start_time")
            .select(
                "uid",
                "start_time",
                "order_id",
            )
            .build()
        return parseUserProfileUbtOrders(tableKVClient.scan(query))
    }

    fun getUbtOrderByRowKeys(detailL2DTOList: List<DetailL2DTO>): List<UserProfileUbtOrder> {
        if (detailL2DTOList.isEmpty()) {
            return emptyList()
        }

        val rowKeys = detailL2DTOList
            .filter { it.type == UbtDataType.ORDER_INDEX.type }
            .map {
                arrayOf(
                    it.uid as Any,
                    it.startTimeMills,
                    it.uniqueId as Any
                )
            }

        if (rowKeys.isEmpty()) {
            return emptyList()
        }

        val list = tableKVClient.batchGet(rowKeys, listOf("uid", "start_time", "order_id", "value"))
        return parseUserProfileUbtOrders(list.iterator())
    }

    private fun parseUserProfileUbtOrders(mapIterator: MutableIterator<MutableMap<String, Any>>): List<UserProfileUbtOrder> {
        return mapIterator.asSequence()
            .mapNotNull { map ->
                try {
                    val uid = map["uid"] as String
                    val startTime = map["start_time"] as Long
                    val rawValue = map["value"] as? String ?: ""
//                    val value = CommonUtil.processJsonValue(
//                        rawValue,
//                        emptyList(),
//                        reduceTokenConfig.removeSpecialChars
//                    )
                    val value = rawValue
                    val orderId = map["order_id"] as? String ?: ""
                    UserProfileUbtOrder(
                        uid = uid,
                        startTime = startTime,
                        value = value,
                        orderId = orderId
                    )
                } catch (e: Exception) {
                    logger.error(
                        "Failed to construct UserProfileUbtOrder for from map: {},{}",
                        map,
                        e
                    )
                    null
                }
            }
            .toList()
    }
}