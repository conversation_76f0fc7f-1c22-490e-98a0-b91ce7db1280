package com.ctrip.ibu.userprofileserving.service.dto

import com.fasterxml.jackson.annotation.JsonProperty

data class AttractionResultDTO(
    val status: Status,
    val attractions: List<Attraction>,
    @JsonProperty("hotel_filters")
    val hotelFilters: List<HotelFilter>,
    @JsonProperty("city_transportation")
    val cityTransportation: List<CityTransportation>
)


data class Status(
    val code: Int,
    val msg: String,
    @JsonProperty("is_success")
    val isSuccess: Boolean? = null
)

data class Attraction(
    @JsonProperty("city_id")
    val cityId: Int,
    @JsonProperty("city_name")
    val cityName: String,
    @JsonProperty("daily_attractions")
    val dailyAttractions: List<DailyAttraction>,
    @JsonProperty("hotel_filters")
    val hotelFilters: List<HotelFilter>? = null
)

data class DailyAttraction(
    val date: String,
    val attractions: List<AttractionItem>,
    val summary: String
)

data class AttractionItem(
    val id: Int,
    val name: String,
    val score: Float,
    @JsonProperty("comment_count")
    val commentCount: Long,
    @JsonProperty("hot_score")
    val hotScore: Float,
    val rank: Float
)

data class HotelFilter(
    @JsonProperty("city_id")
    val cityId: Int,
    @JsonProperty("city_name")
    val cityName: String,
    @JsonProperty("hotel_location_filters")
    val hotelLocationFilters: List<HotelLocationFilter>
)

data class CityTransportation(
    @JsonProperty("city_id")
    val cityId: Int,
    @JsonProperty("daily_transportation")
    val dailyTransportation: List<Transportation>
)

data class Transportation(
    val date: String,
    @JsonProperty("transport_advice")
    val transportAdvice: String,
)

data class HotelLocationFilter(
    val date: String,
    @JsonProperty("filter_id")
    val filterId: String,
    val title: String,
    val type: String,
    val value: String,
)

// 日期数据
data class DailyData(
    val attractionIds: List<Int>,
    val attractionNames: List<String>,
    val recommendText: String,
    val hotelFilterDTO: HotelFilterDTO?,
    val transportation: String?,
)

// 城市数据
data class CityData(
    val cityName: String,
    val dailyData: Map<String, DailyData>
)

// 最终结果
data class AttractionResult(
    val cities: Map<Int, CityData>
)