package com.ctrip.ibu.userprofileserving.service.dto

import com.ctrip.ibu.userprofileserving.soa.CommonRequestContext
import com.ctrip.ibu.userprofileserving.soa.Head
import com.ctrip.ibu.userprofileserving.soa.OrderItem
import com.ctrip.ibu.userprofileserving.soa.Route
import com.ctrip.ibu.userprofileserving.soa.TimeInterval
import com.ctrip.ibu.userprofileserving.soa.Travelers

data class CMTRecReqDTO(
    val uid: String,
    val locale: String,
    val head: Head,
    val context: CommonRequestContext,
    val itineraryKey: String,
    val timeInterval: TimeInterval,
    val travelers: Travelers,
    val route: List<Route>,
    val orderList: List<OrderItem>
)
