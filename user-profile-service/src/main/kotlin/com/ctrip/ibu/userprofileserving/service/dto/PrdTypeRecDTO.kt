package com.ctrip.ibu.userprofileserving.service.dto

import com.fasterxml.jackson.annotation.JsonAlias
import com.fasterxml.jackson.annotation.JsonProperty

data class PrdTypeRecDTO(
    @JsonProperty("day_id")
    val dayId: Int,
    var date: String = "",
    @JsonProperty("schedule_date")
    val scheduleDate: String? = null,
    @JsonProperty("existing_orders")
    val existingOrders: String? = null,
    @JsonProperty("requirement_analysis")
    val requirementAnalysis: String,
    @JsonProperty("daily_schedule")
    @JsonAlias("schedule_items","daily_schedule")
    val dailySchedule: List<DailyScheduleItem>
)


data class DailyScheduleItem(
    @JsonProperty("product_type")
    val productType: String,
    val status: String,
    val parameters: Parameter,
)

data class Parameter(
    @JsonProperty("departure_time")
    val departureTime: String? = null,
    @JsonProperty("subtype")
    val subtype: String? = null,
    @JsonProperty("arrival_time")
    val arrivalTime: String? = null,
    val description: String? = null,
    @JsonProperty("begin_time")
    val beginTime: String? = null,
    @JsonProperty("end_time")
    val endTime: String? = null,
    @JsonProperty("city_name")
    val cityName: String? = null,
    @JsonProperty("city_id")
    val cityId: Int? = null,
    val adult: Int? = null,
    val children: Int? = null,
    val checkin: String? = null,
    val checkout: String? = null,
    @JsonProperty("pick_up_date")
    val pickUpDate: String? = null,
    @JsonProperty("return_date")
    val returnDate: String? = null,
    // other result fields
    var previousHotels:List<DailyScheduleItem>? = null,
    var hotelFilters: List<HotelFilterDTO>? = null,
    var hotelIds: List<Int>? = null,
    var attractionsId: List<Int>? = null,
    var attractionsName: List<String>? = null,
    var recommendText: String? = null,
    var recommendTextRoute: String? = null,
)