package com.ctrip.ibu.userprofileserving.service.dto

import com.fasterxml.jackson.annotation.JsonProperty

data class RecReasonDTO(
    val date: String,
    @JsonProperty("daily_schedule")
    val dailySchedule: List<ReasonDailyScheduleItem>
)

data class ReasonDailyScheduleItem(
    @JsonProperty("product_type")
    val productType: String,
    @JsonProperty("recommend_text")
    val recommendText: String?
)