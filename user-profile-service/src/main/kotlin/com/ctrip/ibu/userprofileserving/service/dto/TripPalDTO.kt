package com.ctrip.ibu.userprofileserving.service.dto

data class IMTextMessage(
    val accountId: String,
    val corpId: String = "ctrip",
    val receiversType: Int = 1,
    val receivers: List<String>,
    val text: String,
)


data class IMAtTextMessage(
    val accountId: String,
    val partnerImId: String,
    val corpId: String = "ctrip",
    val atUserListType: Int = 1,
    val atUserList: List<String>,
    val text: String,
)