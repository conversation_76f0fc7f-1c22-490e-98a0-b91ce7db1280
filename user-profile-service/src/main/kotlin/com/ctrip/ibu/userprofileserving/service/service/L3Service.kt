package com.ctrip.ibu.userprofileserving.service.service

import com.ctrip.ibu.userprofileserving.service.agent.SummaryAgent
import com.ctrip.ibu.userprofileserving.service.config.BatchCallConfig
import com.ctrip.ibu.userprofileserving.service.config.SummaryConfig
import com.ctrip.ibu.userprofileserving.service.dto.IMAtTextMessage
import com.ctrip.ibu.userprofileserving.service.dto.SummaryL3DTO
import com.ctrip.ibu.userprofileserving.service.util.DateUtils
import com.ctrip.ibu.userprofileserving.service.util.JsonUtil
import com.ctrip.ibu.userprofileserving.service.util.OKHttpUtils
import com.ctrip.ibu.userprofileserving.service.vo.SummaryBatchCallRequest
import com.ctrip.ibu.userprofileserving.service.vo.SummaryGetRequest
import com.fasterxml.jackson.databind.ObjectMapper
import jakarta.annotation.Resource
import kotlinx.coroutines.*
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.slf4j.MDCContext
import kotlinx.coroutines.sync.Semaphore
import kotlinx.coroutines.sync.withPermit
import org.apache.commons.lang.StringUtils
import kotlin.system.measureTimeMillis
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import qunar.tc.qconfig.client.spring.QMapConfig
import java.util.concurrent.ThreadPoolExecutor
import java.util.concurrent.atomic.AtomicInteger
import org.slf4j.MDC
import java.util.UUID

@Service
class L3Service(
    private val summaryAgent: SummaryAgent,
    private val summaryService: UserProfileSummaryService,
) {

    companion object {
        val logger: Logger = LoggerFactory.getLogger(L3Service::class.java)
    }

    @QMapConfig("summary.properties")
    lateinit var summaryConfig: SummaryConfig

    @QMapConfig("batch-call.properties")
    lateinit var batchCallConfig: BatchCallConfig

    @Resource(name = "SummaryBatchCallAsyncService")
    lateinit var asyncExecutor: ThreadPoolExecutor

    suspend fun isNeedUpdateL3(
        uid: String,
        callTime: String,
        topic: String,
        topicId: String
    ): Boolean {
        val chunkRanges = summaryAgent.getL2ChunkRanges(uid, callTime, topic, topicId)
        return chunkRanges.isNotEmpty()
    }

    fun isNeedUpdateL3Blocking(
        uid: String,
        callTime: String,
        topic: String,
        topicId: String
    ): Boolean {
        return runBlocking {
            isNeedUpdateL3(uid, callTime, topic, topicId)
        }
    }

    suspend fun generateL3(
        uid: String,
        callTime: String,
        topic: String,
        topicId: String,
        requestId: String
    ): String {
        // 创建一个新的协程上下文，不继承父协程的取消状态
        return withContext(NonCancellable) {
            try {
                // Generate new summary data by AI agent
                val summaries = summaryAgent.call(uid, callTime, topic, topicId, requestId)

                // Return as a map for easier JSON representation
                JsonUtil.toJson(summaries) ?: StringUtils.EMPTY
            } catch (e: Exception) {
                logger.error("Error in generateL3 for uid $uid: ${e.message}", e)
                StringUtils.EMPTY
            }
        }
    }

    fun batchGenerateL3(req: SummaryBatchCallRequest): Map<String, Any> {
        val requestId = UUID.randomUUID().toString()
        MDC.put("request_id", requestId)
        logger.info("BatchGenerateL3 started, request_id={}", requestId)

        try {
            if (req.uids.isEmpty()) {
                return mapOf(
                    "success" to true,
                    "message" to "No uid to process",
                    "requestId" to requestId
                )
            }

            launchBatchProcessing(req, requestId)

            return mapOf(
                "success" to true,
                "message" to "Batch processing started for ${req.uids.size} users",
                "requestId" to requestId
            )
        } finally {
            MDC.remove("request_id")
        }
    }

    private fun launchBatchProcessing(req: SummaryBatchCallRequest, requestId: String) {
        // 创建一个全局作用域来启动后台任务
        // 注意：这个协程不会被应用程序生命周期管理，需要确保它能正确完成或取消
        CoroutineScope(Dispatchers.Default).launch {
            val mdcContext = mapOf("request_id" to requestId)
            withContext(MDCContext(mdcContext)) {
                logger.info("Starting background batch processing, request_id={}", requestId)

                try {
                    processBatchInBackground(req, requestId)
                } catch (e: Exception) {
                    logger.error("Background batch processing failed: ${e.message}", e)
                }
            }
        }
    }

    private suspend fun processBatchInBackground(req: SummaryBatchCallRequest, requestId: String) {
        val (startTime, endTime) = getStartAndEndTime(req.callTime)
        val semaphore = Semaphore(batchCallConfig.semaphore)

        val success = AtomicInteger()
        val failure = AtomicInteger()
        // 添加标记表示是否为正常取消
        var isNormalCancellation = false

        // 创建一个SupervisorJob，这样一个子协程的失败不会影响其他子协程
        val supervisorJob = SupervisorJob()
        // 添加协程异常处理器来记录取消原因
        val exceptionHandler = CoroutineExceptionHandler { _, exception ->
            logger.error("Coroutine exception caught in exception handler: ${exception.message}", exception)
            if (exception is CancellationException) {
                logger.error("Job cancellation detected. Cancellation cause: ${exception.cause?.message}", exception.cause)
            }
        }
        val scope = CoroutineScope(asyncExecutor.asCoroutineDispatcher() + supervisorJob + exceptionHandler)

        // 添加监听器来跟踪Job状态变化
        supervisorJob.invokeOnCompletion { cause ->
            if (cause != null) {
                if (cause is CancellationException && isNormalCancellation) {
                    logger.info("SupervisorJob completed with normal cancellation")
                } else {
                    logger.error("SupervisorJob completed with exception: ${cause.message}", cause)
                }
            } else {
                logger.info("SupervisorJob completed normally")
            }
        }

        val elapsed = measureTimeMillis {
            try {
                // 在新的作用域中启动协程
                val deferreds = req.uids.map { uid ->
                    scope.async {
                        // 添加监听器来跟踪每个任务的状态
                        coroutineContext[Job]?.invokeOnCompletion { cause ->
                            if (cause != null) {
                                if (cause is CancellationException && isNormalCancellation) {
                                    logger.info("Task for uid $uid cancelled normally")
                                } else {
                                    logger.error("Task for uid $uid completed with exception: ${cause.message}", cause)
                                }
                            }
                        }

                        semaphore.withPermit {
                            try {
                                MDC.put("request_id", requestId)
                                MDC.put("uid", uid)
                                logger.info("Processing uid: {}", uid)

                                if (req.isDeleteCache == true) {
                                    summaryService.removeUserProfileSummaryData(
                                        uid, startTime, endTime, req.topicId
                                    )
                                }

                                val result = summaryAgent.call(
                                    uid, req.callTime, req.topic, req.topicId, requestId
                                )

                                success.incrementAndGet()
                                result
                            } catch (e: Exception) {
                                logger.error("Error processing uid: $uid: ${e.message}", e)
                                failure.incrementAndGet()
                                throw e
                            } finally {
                                MDC.clear()
                            }
                        }
                    }
                }

                // 等待所有任务完成，但不传播取消
                deferreds.forEach { deferred ->
                    try {
                        // 记录开始等待时间
                        val startWaitTime = System.currentTimeMillis()

                        deferred.await()

                        // 记录等待结束时间
                        val endWaitTime = System.currentTimeMillis()
                        logger.info("Awaited task completed in ${endWaitTime - startWaitTime}ms")
                    } catch (e: Exception) {
                        logger.error("Error awaiting task: ${e.message}", e)
                        if (e is CancellationException) {
                            logger.error("Task was cancelled. Cancellation cause: ${e.cause?.message}", e.cause)
                        }
                    }
                }
            } finally {
                // 设置标记表示这是正常取消
                isNormalCancellation = true
                // 确保取消所有子协程
                logger.info("Cancelling supervisor job")
                supervisorJob.cancel()
            }
        }

        logger.info("All tasks completed, sending notification...")

        // 发送批处理完成通知
        sendBatchCallNotification(
            req.notifyUser,
            req.uids.size,
            success.get(),
            failure.get(),
            elapsed,
            requestId
        )

        logger.info("Batch processing completed, request_id={}", requestId)
    }

    private fun sendBatchCallNotification(
        user: String?,
        size: Int,
        successCount: Int,
        failCount: Int,
        elapsedTime: Long,
        requestId: String
    ) {
        MDC.put("request_id", requestId)

        try {
            if (user.isNullOrBlank()) {
                logger.info("No user specified for notification, skipping IM notification")
                return
            }

            try {
                val minutes = elapsedTime / 60000
                val seconds = (elapsedTime % 60000) / 1000

                val messageText = """
                    {0}您的批量处理任务已完成:
                    - 总处理数量: $size
                    - 成功数量: $successCount
                    - 失败数量: $failCount
                    - 总耗时: ${minutes}分${seconds}秒
                    - 平均每个用户耗时: ${if (size > 0) elapsedTime / size else 0}毫秒
                """.trimIndent()

                val imMessage = IMAtTextMessage(
                    accountId = batchCallConfig.accountId,
                    partnerImId = batchCallConfig.partnerImId,
                    text = messageText,
                    atUserList = listOf(user)
                )

                val objectMapper = ObjectMapper()
                val jsonBody = objectMapper.writeValueAsString(imMessage)

                logger.info("Sending batch completion notification to user: $user")
                val response = OKHttpUtils.post(
                    url = batchCallConfig.imWebApi,
                    headers = mapOf(
                        "Content-Type" to "application/json",
                        "Accept" to "application/json"
                    ),
                    body = jsonBody
                )

                logger.info("Notification sent successfully, response: $response")
            } catch (e: Exception) {
                logger.error(
                    "Failed to send batch completion notification: ${e.message}",
                    e
                )
            }
        } finally {
            MDC.remove("request_id")
        }
    }

    fun getSummaries(summaryGetRequest: SummaryGetRequest): List<Map<String, List<SummaryL3DTO>>> {
        val triple = getStartAndEndTime(summaryGetRequest.callTime)
        val summaries = summaryGetRequest.uids.map { uid ->
            val summaryList =
                summaryService.getUserProfileSummaryData(uid, triple.first, triple.second, summaryGetRequest.topicId)
            mapOf(uid to summaryList)
        }
        return summaries
    }

    private fun getStartAndEndTime(callTime: String): Pair<Long, Long> {
        val startDateStr = DateUtils.getDateTime(callTime, summaryConfig.daysMax)
        val endDateStr = DateUtils.getDateTime(callTime, 0)

        if (startDateStr.isNullOrBlank() || endDateStr.isNullOrBlank()) {
            logger.error("callTime is invalid, please input correct callTime, example: 2025-04-14 07:58:34.234")
            return Pair(0L, 0L)
        }

        val startTime = DateUtils.parseDateTimeMillis(startDateStr)
        val endTime = DateUtils.parseDateTimeMillis(endDateStr)
        return Pair(startTime, endTime)
    }
}