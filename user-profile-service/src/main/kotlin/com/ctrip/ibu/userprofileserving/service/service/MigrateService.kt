package com.ctrip.ibu.userprofileserving.service.service

import cn.afterturn.easypoi.excel.ExcelImportUtil
import cn.afterturn.easypoi.excel.entity.ImportParams
import com.ctrip.ibu.userprofileserving.service.dto.RecommendReqDTO
import com.ctrip.ibu.userprofileserving.service.dto.TimeCostReqDTO
import org.springframework.stereotype.Service
import org.springframework.web.multipart.MultipartFile
import java.io.ByteArrayInputStream

@Service
class MigrateService(
    private val userProfileCMTRecommendService: UserProfileCMTRecommendService,
    private val userProfileTimeCostService: UserProfileCostTimeService
) {

    companion object{
        private val logger = org.slf4j.LoggerFactory.getLogger(MigrateService::class.java)
    }


    fun migrateRecommendTOMysql(file: MultipartFile) {
        val importParams = ImportParams()
        importParams.startRows = 0
        importParams.startSheetIndex = 0
        val uids = ExcelImportUtil.importExcel<RecommendReqDTO>(
            file.inputStream,
            RecommendReqDTO::class.java,
            importParams
        )
        userProfileCMTRecommendService.migrateOBKVTOMysql(uids.map { it.uid })
    }

    fun migrateTimeCostToMysql(file: MultipartFile) {
        val importParams = ImportParams()
        importParams.startRows = 0
        importParams.startSheetIndex = 0
        val timeCosts = ExcelImportUtil.importExcel<TimeCostReqDTO>(
            file.inputStream,
            TimeCostReqDTO::class.java,
            importParams
        )
        userProfileTimeCostService.migrateTimeCostToMysql(timeCosts)
    }


}