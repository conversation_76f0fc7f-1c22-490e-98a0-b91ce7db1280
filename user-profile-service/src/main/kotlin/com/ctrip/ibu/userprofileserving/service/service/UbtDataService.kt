package com.ctrip.ibu.userprofileserving.service.service

import com.ctrip.ibu.userprofileserving.service.dao.obkv.UserProfileUbtOBKVDao
import com.ctrip.ibu.userprofileserving.service.dao.obkv.UserProfileUbtOrderCompressOBKVDao
import com.ctrip.ibu.userprofileserving.service.dao.obkv.UserProfileUbtOrderOBKVDao
import com.ctrip.ibu.userprofileserving.service.dao.entity.obkv.UserProfileUbt
import com.ctrip.ibu.userprofileserving.service.dao.entity.obkv.UserProfileUbtOrder
import com.ctrip.ibu.userprofileserving.service.dao.entity.obkv.UserProfileUbtOrderCompress
import com.ctrip.ibu.userprofileserving.service.dto.DetailL2DTO
import com.ctrip.ibu.userprofileserving.service.enums.UbtDataType
import com.ctrip.ibu.userprofileserving.service.util.DateUtils.getTimeString
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

@Service
class UbtDataService(
    private val userProfileUbtOBKVDao: UserProfileUbtOBKVDao,
    private val userProfileUbtOrderOBKVDao: UserProfileUbtOrderOBKVDao,
    private val userProfileUbtOrderCompressOBKVDao: UserProfileUbtOrderCompressOBKVDao
) {

    companion object {
        private val logger: Logger = LoggerFactory.getLogger(UbtDataService::class.java)
    }

    fun saveUserProfileUbtData(detailL2DTO: DetailL2DTO) {
        detailL2DTO.let { dto ->
            val userProfileUbt = UserProfileUbt(
                uid = dto.uid,
                startTime = dto.startTimeMills,
                value = dto.value!!,
                eventId = dto.uniqueId
            )
            userProfileUbtOBKVDao.insert(userProfileUbt)

            val userProfileUbtOrder = UserProfileUbtOrder(
                uid = dto.uid,
                startTime = dto.startTimeMills,
                value = dto.value,
                orderId = dto.uniqueId
            )
            userProfileUbtOrderOBKVDao.insert(userProfileUbtOrder)
        }
    }

    fun getUserProfileUbtData(uid: String, startTime: Long, endTime: Long): List<DetailL2DTO> {
        val userProfileUbtList: List<UserProfileUbt> =
            userProfileUbtOBKVDao.getUserProfileUbt(uid, startTime, endTime)
//        val userProfileUbtOrderList: List<UserProfileUbtOrder> =
//            userProfileUbtOrderOBKVDao.getUserProfileUbtOrder(uid, startTime, endTime)
        val userProfileUbtOrderCompressList: List<UserProfileUbtOrderCompress> =
            userProfileUbtOrderCompressOBKVDao.getUserProfileUbtOrderCompress(uid)

        val ubtDTOs = convertUserProfileUbtToDetailL2DTO(userProfileUbtList)
//        val orderDtos = convertUserProfileUbtOrderToDetailL2DTO(userProfileUbtOrderList)
        val orderDTOs = userProfileUbtOrderCompressList.map { order ->
            DetailL2DTO(
                uid = order.uid,
                startTimeMills = order.startTime,
                startTime = getTimeString(order.startTime),
                value = order.value,
                uniqueId = order.orderId,
                type = UbtDataType.ORDER_INDEX.type
            )
        }

        return ubtDTOs + orderDTOs
    }

    fun getUbtByRowKeys(chunkUBTs: List<DetailL2DTO>): List<DetailL2DTO> {
        if (chunkUBTs.isEmpty()) {
            return emptyList()
        }

        val userProfileUbtList: List<UserProfileUbt> = userProfileUbtOBKVDao.getUbtByRowKeys(chunkUBTs)
//        val userProfileUbtOrderList: List<UserProfileUbtOrder> =
//            userProfileUbtOrderOBKVDao.getUbtOrderByRowKeys(chunkUBTs)
        val userProfileUbtOrderList: List<UserProfileUbtOrderCompress> =
            userProfileUbtOrderCompressOBKVDao.getUbtOrderCompressByRowKeys(chunkUBTs)

        val ubtDTOs = convertUserProfileUbtToDetailL2DTO(userProfileUbtList)
        val orderDTOs = userProfileUbtOrderList.map {
            DetailL2DTO(
                uid = it.uid,
                startTimeMills = it.startTime,
                startTime = getTimeString(it.startTime),
                value = it.value,
                uniqueId = it.orderId,
                type = UbtDataType.ORDER_INDEX.type
            )
        }

        return ubtDTOs + orderDTOs
    }

    private fun convertUserProfileUbtToDetailL2DTO(userProfileUbtList: List<UserProfileUbt>): List<DetailL2DTO> {
        return userProfileUbtList.map { ubt ->
            DetailL2DTO(
                uid = ubt.uid,
                startTimeMills = ubt.startTime,
                startTime = getTimeString(ubt.startTime),
                value = ubt.value,
                uniqueId = ubt.eventId,
                type = UbtDataType.CUSTOM_EVENT.type
            )
        }
    }

    private fun convertUserProfileUbtOrderToDetailL2DTO(userProfileUbtOrderList: List<UserProfileUbtOrder>): List<DetailL2DTO> {
        return userProfileUbtOrderList.map { order ->
            DetailL2DTO(
                uid = order.uid,
                startTimeMills = order.startTime,
                startTime = getTimeString(order.startTime),
                value = order.value,
                uniqueId = order.orderId,
                type = UbtDataType.ORDER_INDEX.type
            )
        }
    }
}