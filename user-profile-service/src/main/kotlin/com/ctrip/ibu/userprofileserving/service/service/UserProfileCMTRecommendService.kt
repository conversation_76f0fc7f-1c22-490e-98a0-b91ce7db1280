package com.ctrip.ibu.userprofileserving.service.service

import com.ctrip.ibu.userprofileserving.service.config.CommonConfig
import com.ctrip.ibu.userprofileserving.service.dao.entity.mysql.UserProfileRecommend
import com.ctrip.ibu.userprofileserving.service.dao.obkv.UserProfileCMTRecommendDao
import com.ctrip.ibu.userprofileserving.service.dao.entity.obkv.UserProfileCMTRecommend
import com.ctrip.ibu.userprofileserving.service.dao.mysql.UserProfileRecommendMysqlDao
import com.ctrip.ibu.userprofileserving.service.dao.mysql.UserProfileTimeCostMysqlDao
import com.ctrip.ibu.userprofileserving.service.dao.obkv.UserProfileTimeCostOBKVDao
import com.ctrip.ibu.userprofileserving.service.enums.UserProfileProductType
import org.springframework.stereotype.Service
import qunar.tc.qconfig.client.spring.QMapConfig
import java.time.Instant

@Service
class UserProfileCMTRecommendService(
    private val userProfileCMTRecommendDao: UserProfileCMTRecommendDao,
    private val userProfileRecommendMysqlDao: UserProfileRecommendMysqlDao,
    private val userProfileTimeCostOBKVDao: UserProfileTimeCostOBKVDao,
    private val userProfileTimeCostMysqlDao: UserProfileTimeCostMysqlDao
) {

    companion object {
        private val logger = org.slf4j.LoggerFactory.getLogger(UserProfileCMTRecommendService::class.java)
    }

    @QMapConfig(value = "common.properties")
    lateinit var commonConfig: CommonConfig


    fun saveUserProfileCMTRecommend(userProfileCMTRecommend: UserProfileCMTRecommend) {
        if (commonConfig.isOBKVWrite) {
            userProfileCMTRecommendDao.insert(userProfileCMTRecommend)
        }
        if (commonConfig.isMysqlWrite) {
            userProfileRecommendMysqlDao.saveUserProfileRecommend(userProfileCMTRecommend.toUserProfileRecommend())
        }
    }

    fun deleteUserProfileCMTRecommend(
        uid: String,
        itineraryKey: String,
        requestId: String
    ) {
        if (commonConfig.isOBKVWrite) {
            deleteFromOBKV(uid, itineraryKey, requestId)
        }
        if (commonConfig.isMysqlWrite) {
            deleteFromMySQL(uid, itineraryKey, requestId)
        }
    }


    fun getUserProfileCMTRecommend(
        uid: String,
        itineraryKey: String
    ): List<UserProfileCMTRecommend> {
        return when {
            commonConfig.isOBKVRead -> userProfileCMTRecommendDao.selectUserProfileCMTRecommend(uid, itineraryKey)
            commonConfig.isMysqlRead -> userProfileRecommendMysqlDao.getUserProfileRecommend(uid, itineraryKey)
            else -> {
                logger.warn("Both OBKV and MySQL reads are disabled, returning empty list.")
                emptyList()
            }
        }
    }

    fun getUserProfileCMTRecommendByRequestId(
        uid: String,
        requestId: String
    ): UserProfileCMTRecommend? {
        return when {
            commonConfig.isOBKVRead -> userProfileCMTRecommendDao.selectUserProfileCMTRecommendByRequestId(uid, requestId)
            commonConfig.isMysqlRead -> userProfileRecommendMysqlDao.getUserProfileRecommendByRequestId(uid, requestId)
            else -> {
                logger.warn("Both OBKV and MySQL reads are disabled, returning null.")
                null
            }
        }
    }

    fun migrateOBKVTOMysql(uids: List<String>) {
        if (!commonConfig.isMysqlWrite) {
            logger.error("MySQL write is disabled, migration aborted.")
            return
        }

        userProfileCMTRecommendDao.getAllUserProfileCMTRecommends(uids).forEach { cmtRecommend ->
            try {
                migrateRecommend(cmtRecommend)
            } catch (e: Exception) {
                logger.error("Failed to migrate UserProfileCMTRecommend for uid: ${cmtRecommend.uid}, requestId: ${cmtRecommend.requestId}", e)
            }
        }
        logger.info("Migration from OBKV to MySQL completed for uids: $uids")
    }

    private fun migrateRecommend(cmtRecommend: UserProfileCMTRecommend) {
        userProfileRecommendMysqlDao.saveUserProfileRecommend(cmtRecommend.toUserProfileRecommend())
    }

    private fun UserProfileCMTRecommend.toUserProfileRecommend() = UserProfileRecommend {
        uid = <EMAIL>
        cacheKey = <EMAIL>
        requestId = <EMAIL>
        product = UserProfileProductType.CMT.value
        value = <EMAIL>
        status = <EMAIL>
        createdAt = <EMAIL>?.toInstant() ?: Instant.now()
    }

    private fun deleteFromOBKV(uid: String, itineraryKey: String, requestId: String) {
        userProfileCMTRecommendDao.delete(uid, itineraryKey, requestId)
        userProfileTimeCostOBKVDao.delete(uid, requestId)
    }

    private fun deleteFromMySQL(uid: String, itineraryKey: String, requestId: String) {
        userProfileRecommendMysqlDao.delete(uid, itineraryKey, requestId)
        userProfileTimeCostMysqlDao.delete(uid, requestId)
    }
}