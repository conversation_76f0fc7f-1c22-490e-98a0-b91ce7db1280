package com.ctrip.ibu.userprofileserving.service.service

import com.ctrip.ibu.userprofileserving.service.config.CommonConfig
import com.ctrip.ibu.userprofileserving.service.dao.obkv.UserProfileTimeCostOBKVDao
import com.ctrip.ibu.userprofileserving.service.dao.entity.obkv.UserProfileTimeCost
import com.ctrip.ibu.userprofileserving.service.dao.mysql.UserProfileTimeCostMysqlDao
import com.ctrip.ibu.userprofileserving.service.dto.TimeCostReqDTO
import com.ctrip.ibu.userprofileserving.service.enums.UserProfileProductType
import org.springframework.stereotype.Service
import qunar.tc.qconfig.client.spring.QMapConfig
import java.time.Instant

@Service
class UserProfileCostTimeService(
    private val userProfileTimeCostOBKVDao: UserProfileTimeCostOBKVDao,
    private val userProfileTimeCostMysqlDao: UserProfileTimeCostMysqlDao
) {

    @QMapConfig(value = "common.properties")
    lateinit var commonConfig: CommonConfig

    companion object {
        private val logger = org.slf4j.LoggerFactory.getLogger(UserProfileCostTimeService::class.java)
    }

    fun saveCostTime(timeCost: UserProfileTimeCost) {
        if (commonConfig.isOBKVWrite) {
            userProfileTimeCostOBKVDao.insert(timeCost)
        }
        if (commonConfig.isMysqlWrite) {
            userProfileTimeCostMysqlDao.saveUserProfileTimeCost(timeCost.toMysqlEntity())
        }
    }

    fun getCostTime(uid: String, requestId: String): List<UserProfileTimeCost> {
        if (commonConfig.isOBKVRead) {
            return userProfileTimeCostOBKVDao.getUserProfileTimeCost(uid, requestId)
        }
        if (commonConfig.isMysqlRead) {
            return userProfileTimeCostMysqlDao.getTimeCosts(uid, requestId).map {
                UserProfileTimeCost(
                    uid = it.uid,
                    requestId = it.requestId,
                    costType = it.costType,
                    costTime = it.costTime,
                    input = it.input,
                    output = it.output
                )
            }
        }
        logger.warn("Both OBKV and MySQL reads are disabled, returning empty list.")
        return emptyList()
    }

    fun migrateTimeCostToMysql(timeCostReqDTOs: List<TimeCostReqDTO>) {
        if (!commonConfig.isMysqlWrite) {
            logger.error("MySQL write is disabled, migration aborted.")
            return
        }
        var successCount = 0
        var failureCount = 0
        timeCostReqDTOs.forEach { dto ->
            try {
                val timeCostsFromObkv = userProfileTimeCostOBKVDao.getUserProfileTimeCost(dto.uid, dto.requestId)
                if (timeCostsFromObkv.isEmpty()) {
                    logger.warn("No time cost data found in OBKV for uid: ${dto.uid}, requestId: ${dto.requestId}")
                    return@forEach
                }
                timeCostsFromObkv.forEach { obkvCost ->
                    userProfileTimeCostMysqlDao.saveUserProfileTimeCost(obkvCost.toMysqlEntity())
                }
                successCount++
            } catch (e: Exception) {
                logger.error("Failed to migrate time cost for uid: ${dto.uid}, requestId: ${dto.requestId}", e)
                failureCount++
            }
        }
        logger.info("Time cost migration completed. Success: $successCount, Failure: $failureCount.")
    }

    private fun UserProfileTimeCost.toMysqlEntity() =
        com.ctrip.ibu.userprofileserving.service.dao.entity.mysql.UserProfileTimeCost {
            uid = <EMAIL>
            requestId = <EMAIL>
            product = UserProfileProductType.CMT.value
            costTime = <EMAIL>
            costType = <EMAIL>
            input = <EMAIL>
            output = <EMAIL>
            createdTime = <EMAIL>?.toInstant() ?: Instant.now()
        }
}