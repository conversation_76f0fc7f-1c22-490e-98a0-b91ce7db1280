package com.ctrip.ibu.userprofileserving.service.service

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONException
import com.ctrip.ibu.userprofileserving.service.dao.obkv.UserProfileSummaryOBKVDao
import com.ctrip.ibu.userprofileserving.service.dao.entity.obkv.UserProfileSummary
import com.ctrip.ibu.userprofileserving.service.dto.SummaryL3DTO
import com.ctrip.ibu.userprofileserving.service.util.DateUtils.parseDateTimeMillis
import com.ctrip.ibu.userprofileserving.service.util.JsonUtil
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

@Service
class UserProfileSummaryService(
    private val userProfileSummaryOBKVDao: UserProfileSummaryOBKVDao
) {
    companion object {
        private val logger = LoggerFactory.getLogger(UserProfileSummaryService::class.java)
    }

    fun getUserProfileSummaryData(
        uid: String,
        startTime: Long,
        endTime: Long,
        topicId: String
    ): List<SummaryL3DTO> {
        val summaryList: List<UserProfileSummary> =
            userProfileSummaryOBKVDao.getUserProfileSummary(uid, startTime, endTime, topicId)

        return summaryList.mapNotNull { userProfileSummary ->
            val valueString = userProfileSummary.value ?: return@mapNotNull null
            try {
                JSON.parseObject(valueString, SummaryL3DTO::class.java)
            } catch (e: JSONException) {
                logger.error("Failed to parse UserProfileSummary value: {}", valueString, e)
                null
            }
        }
    }

    fun removeUserProfileSummaryData(
        uid: String,
        startTime: Long,
        endTime: Long,
        topicId: String
    ) {
        userProfileSummaryOBKVDao.deleteSummariesInRange(uid, startTime, endTime, topicId)
    }

    fun batchSaveUserProfileSummaryData(obtSummaries: List<SummaryL3DTO>) {
        val userProfileSummaries: List<UserProfileSummary> = obtSummaries.map { obtSummary ->
            val startMillis = parseDateTimeMillis(obtSummary.startTime)
            val endMillis = parseDateTimeMillis(obtSummary.endTime)
            logger.info(
                "Save new summary data called with uid: {}, startTime: {}, endTime: {}",
                obtSummary.uid, obtSummary.startTime, obtSummary.endTime
            )
            UserProfileSummary(
                uid = obtSummary.uid,
                startTime = startMillis,
                endTime = endMillis,
                topicId = obtSummary.topicId,
                value = JsonUtil.toJson(obtSummary)
            )
        }
        if (userProfileSummaries.isNotEmpty()) {
            userProfileSummaryOBKVDao.batchInsert(userProfileSummaries)
            logger.info("batchSaveUserProfileSummaryData called successfully with {} items.", userProfileSummaries.size)
        } else {
            logger.warn("batchSaveUserProfileSummaryData called with empty or fully filtered list.")
        }
    }
}