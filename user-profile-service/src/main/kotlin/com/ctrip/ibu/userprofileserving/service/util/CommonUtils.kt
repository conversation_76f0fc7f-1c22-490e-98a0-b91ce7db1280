package com.ctrip.ibu.userprofileserving.service.util

import com.alibaba.fastjson.JSON
import com.ctrip.ibu.userprofileserving.service.dto.CMTRecReqDTO

object CommonUtil {

    private val logger = org.slf4j.LoggerFactory.getLogger(CommonUtil::class.java)

    /**
     * Merge all ranges where intersection exists. Left-closed right-closed
     * [[1, 3], [2, 5], [7, 9]] -> [[1, 5], [7, 9]]
     */
    fun mergeTimeRanges(timeRanges: List<Array<Long>>): List<Array<Long>> {
        if (timeRanges.isEmpty()) {
            return emptyList()
        }

        if (timeRanges.size == 1) {
            return listOf(timeRanges[0])
        }

        val sortedRanges = timeRanges.sortedBy { it[0] }
        val result = mutableListOf<Array<Long>>()

        var start = sortedRanges[0][0]
        var end = sortedRanges[0][1]

        for (i in 1 until sortedRanges.size) {
            val range = sortedRanges[i]
            if (end < range[0]) {
                result.add(arrayOf(start, end))
                start = range[0]
                end = range[1]
            } else {
                end = maxOf(end, range[1])
            }
        }

        result.add(arrayOf(start, end))
        return result
    }

    /**
     * From the candidate list, find the element that is not in any of the ranges.
     * Example:
     * list_value=[0,1,2,3,4,5]; list_ranges= [[2, 4]] -> list_missing_index= [0,1,5]
     */
    fun findMissingIndexes(times: List<Long>, ranges: List<Array<Long>>): IntArray {
        if (times.isEmpty()) {
            return IntArray(0)
        }

        // If there is no range limit, return all indexes
        if (ranges.isEmpty()) {
            return IntArray(times.size) { it }
        }

        // Merge overlapping areas first to reduce later judgment
        val mergedRanges = mergeTimeRanges(ranges)
        val missingIndexes = getMissingIndexes(times, mergedRanges)

        return missingIndexes.toIntArray()
    }

    /**
     * Merges consecutive numbers from the entered number list into a range
     * [0, 2, 3, 100, 5, 6, 7, 9] -> [0, 0], [2, 3], [5, 7], [9, 9], [100, 100]
     */
    fun mergeContinuousNumbers(numbers: IntArray): List<IntArray> {
        if (numbers.isEmpty()) {
            return emptyList()
        }

        val sortedUniqueNumbers = numbers.distinct().sorted()

        if (sortedUniqueNumbers.size == 1) {
            return listOf(intArrayOf(sortedUniqueNumbers[0], sortedUniqueNumbers[0]))
        }

        val result = mutableListOf<IntArray>()
        var start = sortedUniqueNumbers[0]
        var end = start

        for (i in 1 until sortedUniqueNumbers.size) {
            val current = sortedUniqueNumbers[i]
            if (current != end + 1) {
                result.add(intArrayOf(start, end))
                start = current
            }
            end = current
        }

        result.add(intArrayOf(start, end))
        return result
    }

    private fun getMissingIndexes(times: List<Long>, mergedRanges: List<Array<Long>>): List<Int> {
        return times.mapIndexedNotNull { index, time ->
            if (mergedRanges.none { range -> time in range[0]..range[1] }) {
                index
            } else {
                null
            }
        }
    }

    fun processJsonValue(
        jsonString: String,
        removeSpecialFields: List<String>,
        removeSpecialChars: List<String>
    ): String {
        if (jsonString.isBlank()) return ""

        try {
            // Step 1: Parse JSON and handle both object and array cases
            if (jsonString.trim().startsWith("[")) {
                // Handle JSON array
                val jsonArray = JSON.parseArray(jsonString)
                // Process each object in the array
                for (i in 0 until jsonArray.size) {
                    val item = jsonArray.getJSONObject(i)
                    removeSpecialFields.forEach { field ->
                        item.remove(field)
                    }
                }
                var processedValue = jsonArray.toString()
                removeSpecialChars.forEach { char ->
                    processedValue = processedValue.replace(char, "")
                }
                return processedValue
            } else {
                // Handle JSON object
                val jsonObject = JSON.parseObject(jsonString)
                removeSpecialFields.forEach { field ->
                    jsonObject.remove(field)
                }
                // Step 2: Replace special characters with empty string
                var processedValue = jsonObject.toString()
                removeSpecialChars.forEach { char ->
                    processedValue = processedValue.replace(char, "")
                }
                return processedValue
            }
        } catch (e: Exception) {
            logger.error("Failed to process JSON value: $jsonString", e)
            return jsonString
        }
    }


    /**
     * Transform CMTRecReqDTO to match external API format
     * Converts orderList from wrapped format to flat array format
     */
    fun transformCMTRequest(req: CMTRecReqDTO): Map<String, Any> {
        val transformedOrderList = req.orderList.flatMap { orderItem ->
            extractOrdersFromItem(orderItem)
        }

        return mapOf(
            "uid" to req.uid,
            "locale" to req.locale,
            "itineraryKey" to req.itineraryKey,
            "timeInterval" to req.timeInterval,
            "travelers" to req.travelers,
            "route" to req.route,
            "orderList" to transformedOrderList
        )
    }

    /**
     * Extract all orders from an OrderItem using reflection for better maintainability
     */
    private fun extractOrdersFromItem(orderItem: Any): List<Any> {
        val orders = mutableListOf<Any>()

        try {
            val clazz = orderItem::class.java
            val fields = clazz.declaredFields

            fields.forEach { field ->
                field.isAccessible = true
                val value = field.get(orderItem)

                when {
                    // Handle List<*> (like flightOrders) - convert to array of order objects
                    value is List<*> && value.isNotEmpty() -> {
                        val orderArray = value.filterNotNull().mapNotNull { item ->
                            extractOrderFromObject(item)
                        }
                        if (orderArray.isNotEmpty()) {
                            orders.add(orderArray)
                        }
                    }
                    // Handle single objects (like airportOrder, hotelOrder, etc.)
                    value != null && field.name.endsWith("Order") -> {
                        extractOrderFromObject(value)?.let { orders.add(it) }
                    }
                }
            }
        } catch (e: Exception) {
            logger.warn("Failed to extract orders using reflection, falling back to manual extraction: ${e.message}")
            // Fallback to manual extraction if reflection fails
            return extractOrdersManually(orderItem)
        }

        return orders
    }

    /**
     * Extract order information from a single order object
     */
    private fun extractOrderFromObject(orderObj: Any): Map<String, Any>? {
        return try {
            val clazz = orderObj::class.java
            val productTypeField = clazz.getDeclaredField("productType")
            val informationField = clazz.getDeclaredField("information")

            productTypeField.isAccessible = true
            informationField.isAccessible = true

            val productType = productTypeField.get(orderObj) as? String
            val information = informationField.get(orderObj)

            if (productType != null && information != null) {
                createOrderMap(productType, information)
            } else null
        } catch (e: Exception) {
            logger.warn("Failed to extract order from object: ${e.message}")
            null
        }
    }

    /**
     * Cleans JSON string by removing escape characters to reduce token consumption.
     * Removes backslashes and double backslashes that are commonly used in JSON escaping.
     */
    fun cleanJsonForPrompt(jsonString: String?): String {
        return jsonString?.replace("\\\\", "")?.replace("\\\"", "\"")?.replace("\\n", " ")?.replace("\\t", " ")
            ?.replace("\\r", " ")?.replace("\\", "")?.replace(Regex("\\s+"), " ")?.trim()
            ?: ""
    }


    /**
     * Fallback manual extraction method
     */
    private fun extractOrdersManually(orderItem: Any): List<Any> {
        // This would contain the original manual extraction logic as a fallback
        // For now, return empty list to avoid compilation errors
        logger.warn("Manual extraction not implemented, returning empty list")
        return emptyList()
    }

    /**
     * Create a standardized order map for external API
     */
    private fun createOrderMap(productType: String, information: Any): Map<String, Any> {
        return mapOf(
            "productType" to productType,
            "information" to information
        )
    }
}