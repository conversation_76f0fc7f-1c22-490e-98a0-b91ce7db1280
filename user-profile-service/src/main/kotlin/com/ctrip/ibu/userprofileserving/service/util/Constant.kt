package com.ctrip.ibu.userprofileserving.service.util

object Constant {
    const val APPID = "100053425"
    const val GEMINI_LABEL_PROJECT = "user-profile-cmt"
    const val EMOSS = "emoss"
    const val VERTEX = "vertex"

    const val CMT_REC_TOTAL_COST_TYPE = "cmt_rec_total"
    const val CMT_REC_ONLY_L3_COST_TYPE = "cmt_rec_l3_update"
    const val L3_SUMMARY_COST_TYPE = "l3_generate"
    const val CMT_DESCRIPTION_COST_TYPE = "cmt_description"
    const val PRODUCT_SEL_COST_TYPE = "product_select_agent"
    const val PRODUCT_SEL_REQUEST_COST_TYPE = "product_select_request"
    const val ATTRACTION_COST_TYPE = "attraction_agent"
    const val REC_REASON_COST_TYPE = "rec_reason_agent"
    const val HOTEL_FILTER_COST_TYPE = "hotel_filter_agent"
    const val CMT_CDP_COST_TYPE = "cdp_request"

    const val L3_DEFAULT_UID = "_timy1mo47oacxs1o"
    const val L3_TOPIC_ID = "0"
    const val L3_TOPIC = "通用信息(与用户行为相关的所有信息)"

    const val CODE_SUCCESS = "0"
    const val CODE_CONTINUE = "1"
    const val CODE_FAIL = "2"
    const val CODE_NO_DATA = "3"
    const val CODE_SUCCESS_MSG = "SUCCESS"
    const val CODE_CONTINUE_MSG = "CONTINUE"
    const val CODE_NO_DATA_MSG = "REQUEST_ID_NOT_FOUND_DATA"
    const val CODE_FAIL_MSG = "INTERNAL_ERROR_CONTACT_OWNER"

    const val CMT_DESCRIBE_PATH = "/api/v1/cmt/description"
    const val CMT_ATTRACTION_PATH = "/api/v1/recommendations/submit"
    const val CMT_ATTRACTION_STATUS_PATH = "/api/v1/recommendations/status/"
    const val CMT_ATTRACTION_ALL_PATH = "/api/v1/recommendations/results/all"
    const val CMT_PRODUCT_SEL_PATH = "/api/v1/products/recommend"

    const val WORKFLOW_OPERATION_FULL = "Full workflow"
    const val WORKFLOW_OPERATION_L3_GENERATE = "L3 generate"
    const val WORKFLOW_OPERATION_CMT_DESCRIPTION = "CMT description"
    const val WORKFLOW_OPERATION_PRODUCT_SELECT = "Product selection"
    const val WORKFLOW_OPERATION_ATTRACTION = "Attraction data"
    const val WORKFLOW_OPERATION_REC_REASON = "Recommendation reason"
    const val WORKFLOW_OPERATION_HOTEL_FILTER = "Hotel filter"


    const val CMT_ATTRACTION_STATUS_SUCCESS = 0
    const val CMT_ATTRACTION_COMPLETED = "completed"

    const val CMT_RECOMMEND_DATA_KEY = "cmt_rec_data"
    const val CMT_ROUTE_INFO_KEY = "cmt_route_info"
    const val CMT_WORKFLOW_DATA_KEY = "cmt_workflow_data"

    const val CMT_DESTINATION_CITY_KEY = "destinationCity"
    const val CMT_DEPARTURE_CITY_KEY = "departureCity"

    const val CMT_RECOMMENDATION_STATUS = "recommend_and_begin_use"
    const val CMT_ORDER_EXIST_STATUS = "order_exist_and_begin_use"
    const val CMT_IN_USE_STATUS = "in_use"
    const val CMT_END_USE_STATUS = "end_use"
    const val CMT_RECOMMEND_TEXT_ROUTE_UBT = "UBT"
    const val CMT_RECOMMEND_TEXT_ROUTE_POI = "POI"
    const val CMT_RECOMMEND_TEXT_ROUTE_DEFAULT = "DEFAULT"

    const val LOCALE_ZH = "zh"
    const val LOCALE_EN = "en"
    const val LOCALE_KO = "ko"
    const val LOCALE_JA = "ja"
    const val LOCALE_TH = "th"

    const val CMT_REC_PRODUCT_SEL_METRIC_SUCCESS = "cmt_rec_product_sel_req_count_success"
    const val CMT_REC_PRODUCT_SEL_METRIC_FAIL = "cmt_rec_product_sel_req_count_fail"
    const val CMT_REC_ATTRACTION_METRIC_SUCCESS = "cmt_rec_attraction_req_count_success"
    const val CMT_REC_ATTRACTION_METRIC_FAIL = "cmt_rec_attraction_req_count_fail"
    const val CMT_REC_HOTEL_FILTER_METRIC_SUCCESS = "cmt_rec_hotel_filter_req_count_success"
    const val CMT_REC_HOTEL_FILTER_METRIC_FAIL = "cmt_rec_hotel_filter_req_count_fail"
    const val CMT_REC_REASON_METRIC_SUCCESS = "cmt_rec_reason_req_count_success"
    const val CMT_REC_REASON_METRIC_FAIL = "cmt_rec_reason_req_count_fail"
}