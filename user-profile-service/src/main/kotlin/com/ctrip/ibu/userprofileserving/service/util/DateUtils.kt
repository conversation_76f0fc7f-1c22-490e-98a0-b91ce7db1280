package com.ctrip.ibu.userprofileserving.service.util

import org.slf4j.LoggerFactory
import java.time.Instant
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter

object DateUtils {
    private val FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
    private val FORMATTER_MILLIS = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS")
    private val DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd")
    private val LOGGER = LoggerFactory.getLogger(DateUtils::class.java)
    private val CHINA_ZONE = ZoneId.of("Asia/Shanghai")

    /**
     * Convert a date string to milliseconds since the epoch
     */
    fun parseDateMillis(dateString: String): Long {
        if (dateString.isEmpty()) return 0L

        return try {
            LocalDateTime.parse(dateString, FORMATTER)
                .atZone(CHINA_ZONE)
                .toInstant()
                .toEpochMilli()
        } catch (e: Exception) {
            LOGGER.error("Error parse date millis '$dateString' to milliseconds", e)
            0L
        }
    }

    fun parseDateTimeMillis(dateString: String?): Long {
        if (dateString.isNullOrEmpty()) {
            LOGGER.error("Error parse date time millis dateString is null or empty")
            return 0L
        }
        return LocalDateTime.parse(dateString, FORMATTER_MILLIS)
            .atZone(CHINA_ZONE)
            .toInstant()
            .toEpochMilli()
    }

    fun now(): String {
        return try {
            LocalDateTime.now(CHINA_ZONE).format(FORMATTER_MILLIS)
        } catch (e: Exception) {
            LOGGER.error("Error getting current date time,{}", e)
            ""
        }
    }

    /**
     * Convert millisecond timestamp to date string (UTC+8)
     */
    fun getTimeString(millis: Long): String {
        return LocalDateTime.ofInstant(Instant.ofEpochMilli(millis), CHINA_ZONE)
            .format(FORMATTER_MILLIS)
    }

    /**
     * Convert millisecond timestamp to date string (UTC+8)
     */
    fun getDateString(millis: Long): String {
        return LocalDateTime.ofInstant(Instant.ofEpochMilli(millis), CHINA_ZONE)
            .format(DATE_FORMATTER)
    }


    /**
     * Calculates a new date by adding a specific number of days to the given date string.
     */
    fun getDateAfterDays(dateString: String?, interval: Int): String? {
        if (dateString.isNullOrEmpty()) return null

        return try {
            LocalDate.parse(dateString, DATE_FORMATTER)
                .plusDays(interval.toLong())
                .format(DATE_FORMATTER)
        } catch (e: Exception) {
            LOGGER.error("Error converting date string '$dateString' to date day", e)
            null
        }
    }

    fun getDateTime(dateString: String?, interval: Int): String? {
        if (dateString.isNullOrEmpty()) return null

        return try {
            LocalDateTime.parse(dateString, FORMATTER_MILLIS)
                .minusDays(interval.toLong())
                .format(FORMATTER_MILLIS)
        } catch (e: Exception) {
            LOGGER.error("Error converting date string '$dateString' to date time", e)
            null
        }
    }

    fun getDiffDays(startDate: String, endDate: String): Long {
        return try {
            val start = LocalDate.parse(startDate, DATE_FORMATTER)
            val end = LocalDate.parse(endDate, DATE_FORMATTER)
            java.time.temporal.ChronoUnit.DAYS.between(start, end)
        } catch (e: Exception) {
            LOGGER.error("Error calculating difference in days between '$startDate' and '$endDate'", e)
            0L
        }
    }

    fun isFutureDate(dateString: String): Boolean {
        return try {
            !LocalDate.parse(dateString, DATE_FORMATTER).isBefore(LocalDate.now())
        } catch (e: Exception) {
            LOGGER.error("Error checking if date '$dateString' is future date", e)
            false
        }
    }

    fun getCalendarDate(dateString: String): LocalDate? {
        return try {
            LocalDate.parse(dateString, DATE_FORMATTER)
        } catch (e: Exception) {
            LOGGER.error("Error parsing date string '$dateString' to LocalDate", e)
            null
        }
    }
}