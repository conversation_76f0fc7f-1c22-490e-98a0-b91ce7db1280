package com.ctrip.ibu.userprofileserving.service.util

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.SerializationFeature
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import org.slf4j.Logger
import org.slf4j.LoggerFactory

object JsonUtil {
    private val mapper = ObjectMapper().apply {
        // Configure mapper for better Kotlin support
        registerKotlinModule()

        // Deserialization settings
        configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
        configure(DeserializationFeature.FAIL_ON_NULL_FOR_PRIMITIVES, false)
        configure(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT, true)

        // Serialization settings
        configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false)
        configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false)

        // Ignore null values during serialization
        setSerializationInclusion(JsonInclude.Include.NON_NULL)
    }

    private val log: Logger = LoggerFactory.getLogger(JsonUtil::class.java)

    /**
     * Serialize an object to JSON string
     * @param obj the object to serialize
     * @return JSON string or null if serialization fails
     */
    fun toJson(obj: Any?): String? {
        return obj?.let {
            runCatching {
                mapper.writeValueAsString(it)
            }.onFailure { exception ->
                log.error("Failed to serialize object to JSON: ${obj::class.simpleName}", exception)
            }.getOrNull()
        }
    }

    /**
     * Serialize an object to JSON string with Result wrapper
     * @param obj the object to serialize
     * @return Result containing JSON string or exception
     */
    fun toJsonResult(obj: Any?): Result<String?> {
        return if (obj == null) {
            Result.success(null)
        } else {
            runCatching {
                mapper.writeValueAsString(obj)
            }.onFailure { exception ->
                log.error("Failed to serialize object to JSON: ${obj::class.simpleName}", exception)
            }
        }
    }

    /**
     * Deserialize JSON string to object using TypeReference
     * @param content JSON string to deserialize
     * @param reference TypeReference for the target type
     * @return deserialized object or null if deserialization fails
     */
    fun <T> fromJson(content: String?, reference: TypeReference<T>): T? {
        return content?.takeIf { it.isNotBlank() }?.let {
            runCatching {
                mapper.readValue(it, reference)
            }.onFailure { exception ->
                log.error("Failed to deserialize JSON to ${reference.type}: $content", exception)
            }.getOrNull()
        }
    }

    /**
     * Deserialize JSON string to object using Class
     * @param content JSON string to deserialize
     * @param clazz target class
     * @return deserialized object or null if deserialization fails
     */
    fun <T> fromJson(content: String?, clazz: Class<T>): T? {
        return content?.takeIf { it.isNotBlank() }?.let {
            runCatching {
                mapper.readValue(it, clazz)
            }.onFailure { exception ->
                log.error("Failed to deserialize JSON to ${clazz.simpleName}: $content", exception)
            }.getOrNull()
        }
    }

    /**
     * Deserialize JSON string to object using reified type parameter
     * @param content JSON string to deserialize
     * @return deserialized object or null if deserialization fails
     */
    inline fun <reified T> fromJson(content: String?): T? {
        return fromJson(content, T::class.java)
    }

    /**
     * Deserialize JSON string to object with Result wrapper
     * @param content JSON string to deserialize
     * @param clazz target class
     * @return Result containing deserialized object or exception
     */
    fun <T> fromJsonResult(content: String?, clazz: Class<T>): Result<T?> {
        return if (content.isNullOrBlank()) {
            Result.success(null)
        } else {
            runCatching {
                mapper.readValue(content, clazz)
            }.onFailure { exception ->
                log.error("Failed to deserialize JSON to ${clazz.simpleName}: $content", exception)
            }
        }
    }

    /**
     * Deserialize JSON string to object with Result wrapper using reified type
     * @param content JSON string to deserialize
     * @return Result containing deserialized object or exception
     */
    inline fun <reified T> fromJsonResult(content: String?): Result<T?> {
        return fromJsonResult(content, T::class.java)
    }

    /**
     * Convert object to pretty-printed JSON string
     * @param obj the object to serialize
     * @return pretty-printed JSON string or null if serialization fails
     */
    fun toPrettyJson(obj: Any?): String? {
        return obj?.let {
            runCatching {
                mapper.writerWithDefaultPrettyPrinter().writeValueAsString(it)
            }.onFailure { exception ->
                log.error("Failed to serialize object to pretty JSON: ${obj::class.simpleName}", exception)
            }.getOrNull()
        }
    }

    /**
     * Check if a string is valid JSON
     * @param content string to validate
     * @return true if valid JSON, false otherwise
     */
    fun isValidJson(content: String?): Boolean {
        return content?.takeIf { it.isNotBlank() }?.let {
            runCatching {
                mapper.readTree(it)
                true
            }.getOrElse { false }
        } ?: false
    }

    /**
     * Deserialize JSON array string to List of objects
     * @param content JSON array string to deserialize
     * @param clazz target class for array elements
     * @return List of deserialized objects or null if deserialization fails
     */
    fun <T> fromJsonArray(content: String?, clazz: Class<T>): List<T>? {
        return content?.takeIf { it.isNotBlank() }?.let {
            runCatching {
                val listType = mapper.typeFactory.constructCollectionType(List::class.java, clazz)
                mapper.readValue<List<T>>(it, listType)
            }.onFailure { exception ->
                log.error("Failed to deserialize JSON array to List<${clazz.simpleName}>: $content", exception)
            }.getOrNull()
        }
    }

    /**
     * Deserialize JSON array string to List of objects with Result wrapper
     * @param content JSON array string to deserialize
     * @param clazz target class for array elements
     * @return Result containing List of deserialized objects or exception
     */
    fun <T> fromJsonArrayResult(content: String?, clazz: Class<T>): Result<List<T>?> {
        return if (content.isNullOrBlank()) {
            Result.success(null)
        } else {
            runCatching {
                val listType = mapper.typeFactory.constructCollectionType(List::class.java, clazz)
                mapper.readValue<List<T>>(content, listType)
            }.onFailure { exception ->
                log.error("Failed to deserialize JSON array to List<${clazz.simpleName}>: $content", exception)
            }
        }
    }

    /**
     * Deserialize JSON array string to List of objects using reified type
     * @param content JSON array string to deserialize
     * @return List of deserialized objects or null if deserialization fails
     */
    inline fun <reified T> fromJsonArray(content: String?): List<T>? {
        return fromJsonArray(content, T::class.java)
    }

    /**
     * Deserialize JSON array string to List of objects with Result wrapper using reified type
     * @param content JSON array string to deserialize
     * @return Result containing List of deserialized objects or exception
     */
    inline fun <reified T> fromJsonArrayResult(content: String?): Result<List<T>?> {
        return fromJsonArrayResult(content, T::class.java)
    }
}