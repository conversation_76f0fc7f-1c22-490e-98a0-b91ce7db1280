package com.ctrip.ibu.userprofileserving.service.util

import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import org.slf4j.LoggerFactory
import java.io.IOException
import java.util.concurrent.TimeUnit

class OKHttpUtils {
    companion object {
        const val OKHTTP_CLIENT_READ_TIMEOUT = 10L
        const val OKHTTP_CLIENT_WRITE_TIMEOUT = 10L
        const val OKHTTP_CLIENT_CONNECT_TIMEOUT = 10L

        private val client: OkHttpClient by lazy {
            OkHttpClient.Builder()
                .connectTimeout(OKHTTP_CLIENT_CONNECT_TIMEOUT, TimeUnit.SECONDS)
                .readTimeout(OKHTTP_CLIENT_READ_TIMEOUT, TimeUnit.SECONDS)
                .writeTimeout(OKHTTP_CLIENT_WRITE_TIMEOUT, TimeUnit.SECONDS)
                .build()
        }

        private val logger = LoggerFactory.getLogger(OKHttpUtils::class.java)

        /**
         * Send a POST request with the given headers and body
         *
         * @param url The URL to send the request to
         * @param headers Map of headers to include in the request
         * @param body The request body as a string
         * @return The response body as a string
         * @throws IOException If an I/O error occurs during the request
         */
        fun post(url: String, headers: Map<String, String>, body: String?): String {
            try {
                // Create a request body
                val mediaType = "application/json; charset=utf-8".toMediaType()
                val requestBody = body?.toRequestBody(mediaType)

                // Build request with headers
                val requestBuilder = Request.Builder()
                    .url(url)

                if (requestBody != null) {
                    requestBuilder.post(requestBody)
                }

                // Add headers
                headers.forEach { (name, value) ->
                    requestBuilder.addHeader(name, value)
                }

                // Execute request
                val request = requestBuilder.build()
                logger.info("Sending POST request to $url")

                client.newCall(request).execute().use { response ->
                    if (!response.isSuccessful) {
                        val errorMsg = "Request failed with code: ${response.code}"
                        logger.error(errorMsg)
                        throw IOException(errorMsg)
                    }

                    val responseBody = response.body?.string() ?: ""
                    logger.debug("Received response: $responseBody")
                    return responseBody
                }
            } catch (e: Exception) {
                logger.error("Error during POST request to $url: ${e.message}", e)
                throw e
            }
        }

        /**
         * Send a POST request with the given headers and body, returning a Result
         *
         * @param url The URL to send the request to
         * @param headers Map of headers to include in the request
         * @param body The request body as a string
         * @return Result containing the response body as a string or an exception
         */
        fun postSafe(url: String, headers: Map<String, String>, body: String?): Result<String> {
            return try {
                // Create a request body
                val mediaType = "application/json; charset=utf-8".toMediaType()
                val requestBody = body?.toRequestBody(mediaType)

                // Build request with headers
                val requestBuilder = Request.Builder()
                    .url(url)

                if (requestBody != null) {
                    requestBuilder.post(requestBody)
                }

                // Add headers
                headers.forEach { (name, value) ->
                    requestBuilder.addHeader(name, value)
                }

                // Execute request
                val request = requestBuilder.build()
                logger.info("Sending POST request to $url")

                client.newCall(request).execute().use { response ->
                    if (!response.isSuccessful) {
                        val errorMsg = "Request failed with code: ${response.code}"
                        logger.error(errorMsg)
                        Result.failure(IOException(errorMsg))
                    } else {
                        val responseBody = response.body?.string() ?: ""
                        logger.debug("Received response: $responseBody")
                        Result.success(responseBody)
                    }
                }
            } catch (e: Exception) {
                logger.error("Error during POST request to $url: ${e.message}", e)
                Result.failure(e)
            }
        }

        /**
         * Send a GET request with the given headers and query parameters
         *
         * @param url The base URL to send the request to
         * @param queryParams Map of query parameters to include in the URL
         * @param headers Map of headers to include in the request
         * @return The response body as a string
         * @throws IOException If an I/O error occurs during the request
         */
        fun get(url: String, queryParams: Map<String, Any> = emptyMap(), headers: Map<String, String> = emptyMap()): String {
            try {
                // Build URL with query parameters
                val urlBuilder = StringBuilder(url)
                if (queryParams.isNotEmpty()) {
                    urlBuilder.append(
                        queryParams.entries.joinToString(prefix = "?", separator = "&") { (key, value) ->
                            "$key=$value"
                        }
                    )
                }
                val fullUrl = urlBuilder.toString()

                // Build request with headers
                val requestBuilder = Request.Builder().url(fullUrl)

                // Add headers
                headers.forEach { (name, value) ->
                    requestBuilder.addHeader(name, value)
                }

                // Execute request
                val request = requestBuilder.build()
                logger.info("Sending GET request to $fullUrl")

                client.newCall(request).execute().use { response ->
                    if (!response.isSuccessful) {
                        val errorMsg = "Request failed with code: ${response.code}"
                        logger.error(errorMsg)
                        throw IOException(errorMsg)
                    }

                    val responseBody = response.body?.string() ?: ""
                    logger.debug("Received response: $responseBody")
                    return responseBody
                }
            } catch (e: Exception) {
                logger.error("Error during GET request to $url: ${e.message}", e)
                throw e
            }
        }

        /**
         * Send a GET request with the given headers and query parameters, returning a Result
         *
         * @param url The base URL to send the request to
         * @param queryParams Map of query parameters to include in the URL
         * @param headers Map of headers to include in the request
         * @return Result containing the response body as a string or an exception
         */
        fun getSafe(url: String, queryParams: Map<String, Any> = emptyMap(), headers: Map<String, String> = emptyMap()): Result<String> {
            return try {
                // Build URL with query parameters
                val urlBuilder = StringBuilder(url)
                if (queryParams.isNotEmpty()) {
                    urlBuilder.append(
                        queryParams.entries.joinToString(prefix = "?", separator = "&") { (key, value) ->
                            "$key=$value"
                        }
                    )
                }
                val fullUrl = urlBuilder.toString()

                // Build request with headers
                val requestBuilder = Request.Builder().url(fullUrl)

                // Add headers
                headers.forEach { (name, value) ->
                    requestBuilder.addHeader(name, value)
                }

                // Execute request
                val request = requestBuilder.build()
                logger.info("Sending GET request to $fullUrl")

                client.newCall(request).execute().use { response ->
                    if (!response.isSuccessful) {
                        val errorMsg = "Request failed with code: ${response.code}"
                        logger.error(errorMsg)
                        Result.failure(IOException(errorMsg))
                    } else {
                        val responseBody = response.body?.string() ?: ""
                        logger.debug("Received response: $responseBody")
                        Result.success(responseBody)
                    }
                }
            } catch (e: Exception) {
                logger.error("Error during GET request to $url: ${e.message}", e)
                Result.failure(e)
            }
        }

    }


}