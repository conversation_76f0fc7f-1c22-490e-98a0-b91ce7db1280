package com.ctrip.ibu.userprofileserving.service.util

import org.apache.commons.lang.exception.ExceptionUtils
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.net.SocketTimeoutException
import java.util.concurrent.TimeoutException

/**
 * Utility class for handling retry logic with timeout exceptions
 * Provides a generic retry mechanism for operations that may fail due to timeout issues
 */
object RetryUtils {

    val logger: Logger = LoggerFactory.getLogger(RetryUtils::class.java)

    const val DEFAULT_MAX_RETRIES = 3
    const val DEFAULT_INITIAL_RETRY_DELAY_MS = 1000L

    /**
     * Execute operation with retry mechanism for timeout exceptions
     * @param operation Operation name for logging
     * @param maxRetries Maximum number of retry attempts (default: 3)
     * @param initialDelayMs Initial delay in milliseconds, will be multiplied by attempt number (default: 1000ms)
     * @param defaultValueProvider Function to provide default value when all retries fail
     * @param block The operation to execute
     * @return Result of the operation or default value on failure
     */
    inline fun <reified R> executeWithRetry(
        operation: String,
        maxRetries: Int = DEFAULT_MAX_RETRIES,
        initialDelayMs: Long = DEFAULT_INITIAL_RETRY_DELAY_MS,
        noinline defaultValueProvider: (() -> R)? = null,
        block: () -> R
    ): R where R : Any {
        repeat(maxRetries) { attempt ->
            try {
                val result = block()
                if (attempt > 0) {
                    logger.info("$operation succeeded on attempt ${attempt + 1}")
                }
                return result
            } catch (e: TimeoutException) {
                handleTimeoutException(e, attempt, operation, maxRetries, initialDelayMs)
            } catch (e: SocketTimeoutException) {
                handleSocketTimeoutException(e, attempt, operation, maxRetries, initialDelayMs)
            } catch (e: Exception) {
                // For non-timeout exceptions, log and return default value immediately
                logger.error("$operation failed with non-retryable exception: ${e.message}", e)
                return defaultValueProvider.getDefaultValue<R>()
            }
        }

        // If all retries failed, return default value
        logger.error("$operation failed after $maxRetries attempts")
        return defaultValueProvider.getDefaultValue<R>()
    }

    /**
     * Execute operation with retry mechanism and custom default response creation
     * @param operation Operation name for logging
     * @param maxRetries Maximum number of retry attempts (default: 3)
     * @param initialDelayMs Initial delay in milliseconds (default: 1000ms)
     * @param createDefaultResponse Function to create default response based on type
     * @param block The operation to execute
     * @return Result of the operation or default response on failure
     */
    inline fun <reified R> executeWithRetryAndDefaultResponse(
        operation: String,
        maxRetries: Int = DEFAULT_MAX_RETRIES,
        initialDelayMs: Long = DEFAULT_INITIAL_RETRY_DELAY_MS,
        noinline createDefaultResponse: () -> R,
        block: () -> R
    ): R where R : Any {
        return executeWithRetry(
            operation = operation,
            maxRetries = maxRetries,
            initialDelayMs = initialDelayMs,
            defaultValueProvider = createDefaultResponse,
            block = block
        )
    }

    /**
     * Handle TimeoutException with retry logic
     */
    fun handleTimeoutException(
        e: TimeoutException,
        attempt: Int,
        operation: String,
        maxRetries: Int,
        initialDelayMs: Long
    ) {
        handleRetryableExceptionInternal(e as Exception, attempt, operation, maxRetries, initialDelayMs)
    }

    /**
     * Handle SocketTimeoutException with retry logic
     */
    fun handleSocketTimeoutException(
        e: SocketTimeoutException,
        attempt: Int,
        operation: String,
        maxRetries: Int,
        initialDelayMs: Long
    ) {
        handleRetryableExceptionInternal(e as Exception, attempt, operation, maxRetries, initialDelayMs)
    }

    /**
     * Internal method to handle retryable exceptions
     */
    private fun handleRetryableExceptionInternal(
        e: Exception,
        attempt: Int,
        operation: String,
        maxRetries: Int,
        initialDelayMs: Long
    ) {
        val currentAttempt = attempt + 1
        if (currentAttempt < maxRetries) {
            val delayMs = initialDelayMs * currentAttempt
            logger.warn(
                "$operation timeout on attempt $currentAttempt/$maxRetries, retrying in ${delayMs}ms: ${e.message}"
            )
            try {
                Thread.sleep(delayMs)
            } catch (ie: InterruptedException) {
                Thread.currentThread().interrupt()
                logger.warn("$operation retry interrupted")
                throw ie
            }
        } else {
            logger.error(
                "$operation timeout after $maxRetries attempts: ${e.message}",
                e
            )
            logger.debug("$operation final exception details: {}", ExceptionUtils.getFullStackTrace(e))
        }
    }

    /**
     * Get default value using provider or create a new instance
     */
    inline fun <reified R> (() -> R)?.getDefaultValue(): R where R : Any {
        return this?.invoke() ?: createDefaultInstance<R>()
    }

    /**
     * Create default instance based on type using reflection
     * This is a fallback when no default value provider is given
     */
    @Suppress("UNCHECKED_CAST")
    inline fun <reified R> createDefaultInstance(): R where R : Any {
        return try {
            R::class.java.getDeclaredConstructor().newInstance()
        } catch (e: Exception) {
            logger.error("Failed to create default instance for type ${R::class.java.simpleName}: ${e.message}")
            throw IllegalArgumentException(
                "Cannot create default instance for type ${R::class.java.simpleName}. " +
                        "Please provide a defaultValueProvider function.", e
            )
        }
    }
}