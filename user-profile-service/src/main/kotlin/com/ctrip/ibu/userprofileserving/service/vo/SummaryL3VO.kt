package com.ctrip.ibu.userprofileserving.service.vo

data class SummaryL3VO(
    val summaryId: String,
    val uid: String,
    val startTime: String,
    val endTime: String,
    val topic: String,
    val summary: String
)

data class SummaryBatchCallRequest(
    val uids: List<String>,
    val topic: String,
    val topicId: String,
    val callTime: String,
    val notifyUser: String,
    val isDeleteCache: Boolean? = false,
)

data class SummaryGetRequest(
    val uids: List<String>,
    val topicId: String,
    val callTime: String,
)