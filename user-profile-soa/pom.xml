<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.ctrip.ibu</groupId>
        <artifactId>user-profile-serving</artifactId>
        <version>0.0.1</version>
    </parent>

    <artifactId>user-profile-soa</artifactId>
    <packaging>war</packaging>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.ctrip.ibu</groupId>
            <artifactId>user-profile-service</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.ctrip.ibu</groupId>
            <artifactId>user-profile-application</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.ops</groupId>
            <artifactId>hickwall-sdk</artifactId>
            <version>0.1.17</version>
            <exclusions>
                <exclusion>
                    <groupId>com.squareup.okio</groupId>
                    <artifactId>okio</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.framework</groupId>
            <artifactId>fx-spring-boot-starter-monitor</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctriposs.baiji</groupId>
            <artifactId>baiji-rpc-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>compiler</artifactId>
                    <groupId>com.github.spullara.mustache.java</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.ctriposs.baiji</groupId>
            <artifactId>baiji-rpc-extensions</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctriposs.baiji</groupId>
            <artifactId>baiji-rpc-server</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>compiler</artifactId>
                    <groupId>com.github.spullara.mustache.java</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>velocity</artifactId>
                    <groupId>org.apache.velocity</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.module</groupId>
            <artifactId>jackson-module-kotlin</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-tomcat</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
            <version>2.4.0</version>
            <exclusions>
                <exclusion>
                    <groupId>org.yaml</groupId>
                    <artifactId>snakeyaml</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.83</version>
        </dependency>

        <dependency>
            <groupId>com.ctrip.ibu</groupId>
            <artifactId>user-profile-serving</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.infosec.kms</groupId>
            <artifactId>kms-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>qunar.common</groupId>
            <artifactId>common-http</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlinx</groupId>
            <artifactId>kotlinx-coroutines-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlinx</groupId>
            <artifactId>kotlinx-coroutines-reactor</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>annotations</artifactId>
                    <groupId>org.jetbrains</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlinx</groupId>
            <artifactId>kotlinx-coroutines-slf4j</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
        </dependency>
    </dependencies>
</project>
