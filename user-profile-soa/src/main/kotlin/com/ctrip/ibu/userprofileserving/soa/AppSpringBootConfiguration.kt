package com.ctrip.ibu.userprofileserving.soa

import com.dtp.core.spring.EnableDynamicTp
import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.runApplication

@SpringBootApplication(scanBasePackages = ["com.ctrip.ibu.userprofileserving.*"])
@EnableDynamicTp
class AppSpringBootConfiguration {
}

fun main(args: Array<String>) {
    // TODO: Remove before deployment: enable Artemis local mode for local service testing;
    System.setProperty("artemis.client.local.enabled", "true")
    runApplication<AppSpringBootConfiguration>(*args)
}