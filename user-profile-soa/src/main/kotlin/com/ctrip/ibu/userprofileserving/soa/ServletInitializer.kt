package com.ctrip.ibu.userprofileserving.soa

import org.springframework.boot.builder.SpringApplicationBuilder
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer
import kotlin.jvm.java

class ServletInitializer : SpringBootServletInitializer() {

	override fun configure(application: SpringApplicationBuilder): SpringApplicationBuilder {
		return application.sources(AppSpringBootConfiguration::class.java)
	}

}
