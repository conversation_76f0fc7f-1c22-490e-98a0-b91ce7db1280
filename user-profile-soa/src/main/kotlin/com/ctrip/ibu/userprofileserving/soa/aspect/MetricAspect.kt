package com.ctrip.ibu.userprofileserving.soa.aspect

import io.dropwizard.metrics5.MetricName
import io.dropwizard.metrics5.MetricRegistry
import org.aspectj.lang.ProceedingJoinPoint
import org.aspectj.lang.annotation.Around
import org.aspectj.lang.annotation.Aspect
import org.springframework.stereotype.Component

/**
 * Aspect for monitoring controller requests.
 */
@Aspect
@Component
class MetricAspect(
    private val metric: MetricRegistry
) {


    /**
     * Aspect that monitors all controller requests.
     *
     * This aspect monitors all controller requests, including GET, POST, PUT, DELETE, and PATCH.
     * It creates a timer metric for each request and tags the timer with the controller class name
     * and the method name. It then starts the timer before the controller method is called, and
     * stops the timer after the method has been called.
     *
     * The timer metric is named `controller_request`, and has the following tags:
     * - `controller`: the simple name of the controller class
     * - `method`: the name of the controller method
     */
    @Around(
        "@annotation(org.springframework.web.bind.annotation.RequestMapping) || " +
                "@annotation(org.springframework.web.bind.annotation.GetMapping) || " +
                "@annotation(org.springframework.web.bind.annotation.PostMapping) || " +
                "@annotation(org.springframework.web.bind.annotation.PutMapping) || " +
                "@annotation(org.springframework.web.bind.annotation.DeleteMapping) || " +
                "@annotation(org.springframework.web.bind.annotation.PatchMapping)"
    )
    @Throws(Throwable::class)
    fun monitorRequest(joinPoint: ProceedingJoinPoint): Any? {
        // Get the names of the controller class and method
        val className = joinPoint.target.javaClass.simpleName
        val methodName = joinPoint.signature.name

        // Create a timer metric with the names of the controller class and method as tags
        val tags = mapOf(
            "controller" to className,
            "method" to methodName
        )
        val metricName = MetricName.build("controller_request").tagged(tags)
        val timer = metric.timer(metricName)

        // Start a timer and call the controller method
        val context = timer.time()
        try {
            return joinPoint.proceed()
        } finally {
            // Stop the timer after the controller method has been called
            context.stop()
        }
    }
}