package com.ctrip.ibu.userprofileserving.soa.config

import io.swagger.v3.oas.models.OpenAPI
import io.swagger.v3.oas.models.info.Contact
import io.swagger.v3.oas.models.info.Info
import org.springdoc.core.models.GroupedOpenApi
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration


/**
 * Swagger API configuration.
 */
@Configuration
class SwaggerAPIConfig {

    @Bean
    fun openAPI(): OpenAPI? {
        return OpenAPI().info(
            Info().title("User Profile Serving API")
                .description("")
                .version("v1.0.0")
                .contact(
                    Contact()
                        .name("Ctrip IBU Team")
                        .email("<EMAIL>")
                )
        )
    }

    @Bean
    fun openApi(): GroupedOpenApi? {
        return GroupedOpenApi.builder()
            .group("ai")
            .pathsToMatch("/ai/**", "/v2/**")
            .build()
    }

    @Bean
    fun summaryApi(): GroupedOpenApi? {
        return GroupedOpenApi.builder()
            .group("summary")
            .pathsToMatch("/summary/**")
            .build()
    }

    @Bean
    fun cmtApi(): GroupedOpenApi? {
        return GroupedOpenApi.builder()
            .group("CMT")
            .pathsToMatch("/cmt/**")
            .build()
    }
}