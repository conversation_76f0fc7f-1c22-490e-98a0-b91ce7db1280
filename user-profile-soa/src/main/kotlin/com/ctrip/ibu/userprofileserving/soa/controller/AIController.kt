package com.ctrip.ibu.userprofileserving.soa.controller

import com.ctrip.ibu.userprofileserving.application.soaclient.CsLLMServiceClient
import com.ctrip.ibu.userprofileserving.service.agent.AIClient
import com.ctrip.ibu.userprofileserving.service.config.AIConfig
import com.ctrip.ibu.userprofileserving.service.config.PromptParse
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import qunar.tc.qconfig.client.TypedConfig
import qunar.tc.qconfig.client.spring.QMapConfig


@RestController
@RequestMapping("/ai")
class AIController(
    private val aiClient: AIClient,
    private val csLLMServiceClient: CsLLMServiceClient
) {

    @QMapConfig(value = "ai.properties")
    lateinit var aiConfig: AIConfig


    @PostMapping("/1.0/chat")
    @Operation(summary = "Chat with AI", description = "Chat with AI API")
    suspend fun chat(
        @Parameter(description = "prompt", example = "You are a helpful assistant.")
        prompt: String?,
        @Parameter(description = "message", example = "Hello, how are you?")
        message: String,
        @Parameter(description = "client name like emoss,vertex etc", example = "emoss", required = false)
        clientName: String
    ): String {
        return buildList {
            prompt?.let { add(mapOf("system" to it)) }
            add(mapOf("user" to message))
        }.let { aiClient.generateResponse(it, clientName) }
    }


    @GetMapping("/1.0/config")
    @Operation(summary = "Get AI Configurations", description = "Get AI configurations")
    fun config(): Map<String, String> {
        val prompt = TypedConfig.get("prompt.yml", PromptParse())
        val properties = prompt.current()

        return buildMap {
            put("ai.apikey", aiConfig.apikey ?: "")
            put("ai.baseurl", aiConfig.baseurl ?: "")
            put("ai.model", aiConfig.model ?: "")
            put("ai.temperature", aiConfig.temperature?.toString() ?: "")
            put("ai.max_tokens", aiConfig.maxTokens?.toString() ?: "")
            put("summary.prompt", properties.system ?: "")
            put("summary.user", properties.user ?: "")
        }
    }


    @GetMapping("/1.0/cmllm")
    fun cmLLM(
        @Parameter(description = "message", example = "Hello, how are you?")
        message: String,
        @Parameter(description = "promptCode", example = "267_cmt_agent_demo")
        promptCode: String
    ): String {
        return csLLMServiceClient.generateAndRunChatPrompt(promptCode, mapOf("text" to message))
    }
}