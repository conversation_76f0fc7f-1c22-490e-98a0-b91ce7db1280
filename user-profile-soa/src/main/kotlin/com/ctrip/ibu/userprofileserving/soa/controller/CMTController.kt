package com.ctrip.ibu.userprofileserving.soa.controller

import com.ctrip.ibu.userprofileserving.application.agent.cmt.AttractionAgent
import com.ctrip.ibu.userprofileserving.application.agent.cmt.ProductSelAgent
import com.ctrip.ibu.userprofileserving.application.dto.CMTHotelFilterRequest
import com.ctrip.ibu.userprofileserving.application.dto.CMTHotelRequest
import com.ctrip.ibu.userprofileserving.application.dto.CMTHotelResponse
import com.ctrip.ibu.userprofileserving.application.service.CDPDataService
import com.ctrip.ibu.userprofileserving.application.service.CMTHotelService
import com.ctrip.ibu.userprofileserving.service.dao.entity.obkv.UserProfileTimeCost
import com.ctrip.ibu.userprofileserving.service.dto.AttractionResult
import com.ctrip.ibu.userprofileserving.service.dto.CMTRecReqDTO
import com.ctrip.ibu.userprofileserving.service.dto.HotelFilterUnit
import com.ctrip.ibu.userprofileserving.service.dto.PrdTypeRecDTO
import com.ctrip.ibu.userprofileserving.service.dto.WorkflowResult
import com.ctrip.ibu.userprofileserving.service.service.UserProfileCMTRecommendService
import com.ctrip.ibu.userprofileserving.service.service.UserProfileCostTimeService
import com.ctrip.ibu.userprofileserving.soa.CommonRequestContext
import com.ctrip.ibu.userprofileserving.soa.GetCMTRecommendDataReqType
import com.ctrip.ibu.userprofileserving.soa.GetCMTRecommendDataRespType
import com.ctrip.ibu.userprofileserving.soa.SubmitCMTRecommendReqType
import com.ctrip.ibu.userprofileserving.soa.service.CMTInternalService
import com.ctrip.ibu.userprofileserving.soa.service.CMTSOAServiceImpl
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.slf4j.LoggerFactory
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import java.util.UUID

/**
 * CMT (Customer Management Tool) recommendation controller
 * Provides REST endpoints for hotel recommendations, attraction data, and recommendation management
 */
@RestController
@RequestMapping("/cmt")
@Tag(name = "CMT recommendation", description = "Recommendation API for CMT")
class CMTController(
    private val cmtSOAServiceImpl: CMTSOAServiceImpl,
    private val cdpDataService: CDPDataService,
    private val cmtInternalService: CMTInternalService,
    private val attractionAgent: AttractionAgent,
    private val userProfileCostTimeService: UserProfileCostTimeService,
    private val userProfileCMTRecommendService: UserProfileCMTRecommendService,
    private val cmtHotelService: CMTHotelService,
    private val productSelAgent: ProductSelAgent
) {

    companion object {
        private val logger = LoggerFactory.getLogger(CMTController::class.java)

        // Hotel filter exclusion types
        private val EXCLUDED_HOTEL_FILTER_TYPES = setOf("2", "17", "18")
    }

    // ────────────────── Hotel Endpoints ──────────────────

    @Operation(
        summary = "Get CMT hotel recommendations",
        description = "Retrieves personalized hotel recommendations based on user profile"
    )
    @PostMapping("/1.0/hotels")
    fun getHotels(@RequestBody @Validated request: CMTHotelRequest): CMTHotelResponse {
        logger.info("CMT hotel request received: $request")
        return executeWithErrorHandling("CMT hotel request") {
            cmtHotelService.getHotels(request)
        }
    }

    @PostMapping("/1.0/hotels/filters")
    fun getHotelFilters(@RequestBody @Validated request: CMTHotelFilterRequest): List<HotelFilterUnit> {
        logger.info("CMT hotel filter request received: $request")
        return executeWithErrorHandling("CMT hotel filter request") {
            cmtHotelService.getHotelFilters(request)
                .filterNot { it.type in EXCLUDED_HOTEL_FILTER_TYPES }
        }
    }

    // ────────────────── CDP Endpoints ──────────────────

    @GetMapping("/1.0/cdp")
    fun getCDPData(@RequestParam uid: String): String {
        logger.info("CDP data request for uid: $uid")
        return cdpDataService.getCDPData(uid, UUID.randomUUID().toString())
    }

    // ────────────────── Recommendation Endpoints ──────────────────

    @PostMapping("/1.0/rec:submit")
    fun submitCMTReq(@RequestBody req: SubmitCMTRecommendReqType): Map<String, Any> {
        logger.info("CMT recommendation submit request for uid: ${req.context.uid}")
        val resp = cmtSOAServiceImpl.submitCMTRecommendReq(req)
        return mapOf(
            "resultCode" to resp.resultCode,
            "resultMsg" to resp.resultMsg,
            "requestId" to resp.requestId
        )
    }

    @PostMapping("/1.0/rec/time-cost")
    fun countTimeCost(
        @RequestBody req: SubmitCMTRecommendReqType
    ): Map<String, Any> {
        val uid = req.context.uid
        logger.info("CMT recommendation time cost polling started for uid: $uid")

        val startTime = System.currentTimeMillis()

        return try {
            // Submit CMT recommendation request
            val resp = cmtSOAServiceImpl.submitCMTRecommendReq(req)
            val requestId = resp.requestId
            logger.info("CMT recommendation submitted for uid: $uid, requestId: $requestId")

            // Create request for polling
            val getDataReq = createGetCMTRecommendDataRequest(uid, requestId)

            // Polling configuration
            val maxPollingAttempts = 30  // Maximum polling attempts
            val pollingIntervalMs = 2000L  // 2 seconds between polls
            var pollingCount = 0

            // Start polling
            var recData = cmtSOAServiceImpl.getCMTRecommendData(getDataReq)
            pollingCount++
            logger.info("Initial polling attempt for uid: $uid, requestId: $requestId, resultCode: ${recData.resultCode}")

            // Continue polling until success or max attempts reached
            while (recData.resultCode != "0" && pollingCount < maxPollingAttempts) {
                logger.info("Polling attempt $pollingCount/$maxPollingAttempts for uid: $uid, requestId: $requestId, resultCode: ${recData.resultCode}")

                // Wait before next poll
                Thread.sleep(pollingIntervalMs)

                // Poll again
                recData = cmtSOAServiceImpl.getCMTRecommendData(getDataReq)
                pollingCount++
            }

            val totalCostTime = System.currentTimeMillis() - startTime

            // Log final result
            if (recData.resultCode == "0") {
                logger.info("CMT recommendation polling completed successfully for uid: $uid, requestId: $requestId, total attempts: $pollingCount, total time: ${totalCostTime}ms")
            } else {
                logger.warn("CMT recommendation polling reached max attempts for uid: $uid, requestId: $requestId, final resultCode: ${recData.resultCode}, total attempts: $pollingCount, total time: ${totalCostTime}ms")
            }

            // Save cost time for performance monitoring
            saveCMTPollingCostTime(uid, requestId, totalCostTime, pollingCount, recData.resultCode)

            // Build response
            buildPollingResponse(recData, totalCostTime, pollingCount, requestId)

        } catch (e: Exception) {
            val totalCostTime = System.currentTimeMillis() - startTime
            logger.error("Error during CMT recommendation polling for uid: $uid: ${e.message}", e)

            // Save error cost time
            saveCMTPollingCostTime(uid, "unknown", totalCostTime, 0, "ERROR")

            mapOf(
                "resultCode" to "ERROR",
                "resultMsg" to "Polling failed: ${e.message}",
                "totalCostTimeMs" to totalCostTime,
                "pollingCount" to 0
            )
        }
    }

    @PostMapping("/1.0/rec/assemble")
    fun assembleCMTRecommendData(@RequestBody req: SubmitCMTRecommendReqType) {
        logger.info("CMT recommendation assemble request for uid: ${req.context.uid}")
        val cmtRecReqDTO = req.toCMTRecReqDTO()
        val mainResult = createEmptyWorkflowResult()
        val requestId = UUID.randomUUID().toString()
        cmtInternalService.assembleCMTDefaultData(requestId, cmtRecReqDTO, mainResult)
    }

    @GetMapping("/1.0/rec/data")
    fun getCMTRecommendData(
        @RequestParam uid: String,
        @RequestParam requestId: String
    ): Map<String, Any> {
        logger.info("CMT recommendation data request - uid: $uid, requestId: $requestId")

        val req = createGetCMTRecommendDataRequest(uid, requestId)
        val recData = cmtSOAServiceImpl.getCMTRecommendData(req)
        val timeCosts = userProfileCostTimeService.getCostTime(uid, requestId)

        return buildRecommendDataResponse(recData, timeCosts)
    }

    // ────────────────── Attraction Endpoints ──────────────────

    @PostMapping("/1.0/attraction")
    suspend fun getAttractionData(@RequestBody req: SubmitCMTRecommendReqType): AttractionResult? {
        logger.info("Attraction data request for uid: ${req.context.uid}")

        val cmtRecReqDTO = req.toCMTRecReqDTO()
        val requestId = UUID.randomUUID().toString()

        val result = cmtInternalService.getCMTDescription(cmtRecReqDTO, requestId)
        logger.info("CMT description result: $result")

        val attractionResult = attractionAgent.getAttractionData(cmtRecReqDTO, result, requestId)
        logger.info("Attraction result: $attractionResult")
        return attractionResult
    }

    @PostMapping("/1.0/product/sel")
    suspend fun getProductSelData(
        @RequestBody req: SubmitCMTRecommendReqType
    ): List<PrdTypeRecDTO> {
        logger.info("Product selection request for uid: ${req.context.uid}")
        val cmtRecReqDTO = req.toCMTRecReqDTO()
        val requestId = UUID.randomUUID().toString()

        val description = cmtInternalService.getCMTDescription(cmtRecReqDTO, requestId)
        logger.info("CMT description: $description")
        val result = productSelAgent.getProductSelDataByRequest(cmtRecReqDTO, requestId, description)
        logger.info("Product selection result: $result")
        return result
    }

    @PostMapping("/1.0/rec:remove")
    fun removeCMTRecCache(uid: String, itineraryKey: String, requestId: String) {
        logger.info("Removing CMT recommendation cache for uid: $uid, itineraryKey: $itineraryKey, requestId: $requestId")
        userProfileCMTRecommendService.deleteUserProfileCMTRecommend(uid, itineraryKey, requestId)
    }

    // ────────────────── Helper Methods ──────────────────

    /**
     * Execute operation with standardized error handling
     */
    private inline fun <T> executeWithErrorHandling(operation: String, block: () -> T): T {
        return try {
            block()
        } catch (e: Exception) {
            logger.error("Error processing $operation: ${e.message}", e)
            throw e
        }
    }

    /**
     * Create an empty workflow result for default data assembly
     */
    private fun createEmptyWorkflowResult() = WorkflowResult(
        productSelData = emptyList(),
        hotelFilterData = emptyMap(),
        attractionData = null,
        recReasonData = emptyList()
    )

    /**
     * Create GetCMTRecommendDataReqType from parameters
     */
    private fun createGetCMTRecommendDataRequest(uid: String, requestId: String) =
        GetCMTRecommendDataReqType().apply {
            context = CommonRequestContext().apply { this.uid = uid }
            this.requestId = requestId
        }

    /**
     * Build recommendation data response based on available data
     */
    private fun buildRecommendDataResponse(
        recData: GetCMTRecommendDataRespType,
        timeCosts: Any?
    ): Map<String, Any> {
        val baseResponse = mapOf(
            "resultCode" to recData.resultCode,
            "resultMsg" to recData.resultMsg
        )

        return when {
            recData.data != null && recData.route != null -> baseResponse + mapOf(
                "route" to recData.route,
                "data" to recData.data,
                "timeCosts" to timeCosts
            )

            recData.data != null -> baseResponse + mapOf("data" to recData.data)
            else -> baseResponse
        }
    }

    /**
     * Save CMT polling cost time for performance monitoring
     */
    private fun saveCMTPollingCostTime(
        uid: String,
        requestId: String,
        totalCostTime: Long,
        pollingCount: Int,
        resultCode: String
    ) {
        try {
            val input = "uid=$uid, requestId=$requestId"
            val output = "pollingCount=$pollingCount, resultCode=$resultCode, totalCostTime=${totalCostTime}ms"

            userProfileCostTimeService.saveCostTime(
                UserProfileTimeCost(
                    uid = uid,
                    requestId = requestId,
                    costType = "cmt_polling",
                    costTime = totalCostTime,
                    input = input,
                    output = output
                )
            )
            logger.debug("Saved CMT polling cost time: ${totalCostTime}ms for uid: $uid, requestId: $requestId")
        } catch (e: Exception) {
            logger.error("Failed to save CMT polling cost time for uid: $uid, requestId: $requestId: ${e.message}", e)
        }
    }

    /**
     * Build polling response with timing and polling information
     */
    private fun buildPollingResponse(
        recData: GetCMTRecommendDataRespType,
        totalCostTime: Long,
        pollingCount: Int,
        requestId: String
    ): Map<String, Any> {
        val baseResponse = mapOf(
            "resultCode" to recData.resultCode,
            "resultMsg" to recData.resultMsg,
            "requestId" to requestId,
            "totalCostTimeMs" to totalCostTime,
            "pollingCount" to pollingCount
        )

        return when {
            recData.data != null && recData.route != null -> baseResponse + mapOf(
                "route" to recData.route,
                "data" to recData.data
            )

            recData.data != null -> baseResponse + mapOf("data" to recData.data)
            else -> baseResponse
        }
    }
}

/**
 * Extension function to convert SubmitCMTRecommendReqType to CMTRecReqDTO
 */
private fun SubmitCMTRecommendReqType.toCMTRecReqDTO() = CMTRecReqDTO(
    uid = context.uid,
    locale = context.locale,
    context = context,
    head = head,
    itineraryKey = itineraryKey,
    timeInterval = timeInterval,
    travelers = travelers,
    route = route,
    orderList = orderList
)
