package com.ctrip.ibu.userprofileserving.soa.controller

import com.ctrip.ibu.userprofileserving.service.service.MigrateService
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.IO
import kotlinx.coroutines.launch
import org.springframework.http.MediaType
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestPart
import org.springframework.web.bind.annotation.RestController
import org.springframework.web.multipart.MultipartFile

@RestController
@RequestMapping("/migrate")
class MigrateController(
    private val migrateService: MigrateService
) {
    private val scope = CoroutineScope(Dispatchers.IO)


    @PostMapping("/1.0/recommend", consumes = [MediaType.MULTIPART_FORM_DATA_VALUE])
    suspend fun migrateRecommendTOMysql(
        @RequestPart("file") file: MultipartFile,
    ): Map<String, String> {
        scope.launch {
            migrateService.migrateRecommendTOMysql(file)
        }
        return buildMap {
            put("status", "success")
            put("message", "Migration of recommendations to MySQL is running")
        }
    }

    @PostMapping("/1.0/timeCost", consumes = [MediaType.MULTIPART_FORM_DATA_VALUE])
    suspend fun migrateTimeCostToMysql(
        @RequestPart("file") file: MultipartFile,
    ): Map<String, String> {
        scope.launch {
            migrateService.migrateTimeCostToMysql(file)
        }
        return buildMap {
            put("status", "success")
            put("message", "Migration of time costs to MySQL is running")
        }
    }
}