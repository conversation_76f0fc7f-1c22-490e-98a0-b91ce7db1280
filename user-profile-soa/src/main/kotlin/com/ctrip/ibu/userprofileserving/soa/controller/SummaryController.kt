package com.ctrip.ibu.userprofileserving.soa.controller

import com.ctrip.ibu.userprofileserving.service.dto.SummaryL3DTO
import com.ctrip.ibu.userprofileserving.service.service.L3Service
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.tags.Tag
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import com.ctrip.ibu.userprofileserving.service.vo.SummaryBatchCallRequest
import com.ctrip.ibu.userprofileserving.service.vo.SummaryGetRequest
import org.slf4j.MDC
import org.springframework.web.bind.annotation.RequestBody
import java.util.UUID

@RestController
@RequestMapping("/summary")
@Tag(name = "Summary", description = "Summary API")
class SummaryController(
    private val l3Service: L3Service
) {


    companion object {
        val logger: Logger = LoggerFactory.getLogger(SummaryController::class.java)
    }

    @PostMapping("/1.0/call")
    @Operation(summary = "Call Summary Agent", description = "Call Summary Agent to generate summary data")
    suspend fun call(
        @Parameter(description = "user id", example = "_tihkoxcdpajuo1i") uid: String,
        @Parameter(description = "call time", example = "2025-04-14 07:58:34.234") callTime: String,
        @Parameter(description = "topic", example = "hotel preference") topic: String,
        @Parameter(description = "topic id", example = "1") topicId: String
    ): String {
        val result = l3Service.generateL3(uid, callTime, topic, topicId, UUID.randomUUID().toString())
        MDC.clear()
        return result
    }


    @PostMapping("/1.0/call-batch")
    @Operation(
        summary = "Call Summary Agent in Batch",
        description = "Asynchronously call Summary Agent to generate summary data for multiple users with controlled concurrency"
    )
    suspend fun batchCall(
        @RequestBody batchCallRequest: SummaryBatchCallRequest
    ): Map<String, Any> {
        return l3Service.batchGenerateL3(batchCallRequest)
    }

    @PostMapping("/1.0/result")
    @Operation(
        summary = "Get Summary Result",
        description = "Get summary result for a specific user and time range"
    )
    fun getSummaries(
        @RequestBody summaryGetRequest: SummaryGetRequest
    ): List<Map<String, List<SummaryL3DTO>>> {
        return l3Service.getSummaries(summaryGetRequest)
    }
}