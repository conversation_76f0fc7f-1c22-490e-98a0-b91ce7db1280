package com.ctrip.ibu.userprofileserving.soa.service


import com.ctrip.ibu.userprofileserving.application.dto.CMTHotelRequest
import com.ctrip.ibu.userprofileserving.application.service.CMTHotelService
import com.ctrip.ibu.userprofileserving.service.config.CommonConfig
import com.ctrip.ibu.userprofileserving.service.config.RecommendReasonParse
import com.ctrip.ibu.userprofileserving.service.dao.entity.obkv.UserProfileCMTRecommend
import com.ctrip.ibu.userprofileserving.service.dao.entity.obkv.UserProfileTimeCost
import com.ctrip.ibu.userprofileserving.service.dto.AttractionResult
import com.ctrip.ibu.userprofileserving.service.dto.CMTRecReqDTO
import com.ctrip.ibu.userprofileserving.service.dto.DailyScheduleItem
import com.ctrip.ibu.userprofileserving.service.dto.HotelFilterDTO
import com.ctrip.ibu.userprofileserving.service.dto.Parameter
import com.ctrip.ibu.userprofileserving.service.dto.PrdTypeRecDTO
import com.ctrip.ibu.userprofileserving.service.dto.RecReasonDTO
import com.ctrip.ibu.userprofileserving.service.dto.WorkflowResult
import com.ctrip.ibu.userprofileserving.service.enums.CMTProductType
import com.ctrip.ibu.userprofileserving.service.service.UserProfileCMTRecommendService
import com.ctrip.ibu.userprofileserving.service.service.UserProfileCostTimeService
import com.ctrip.ibu.userprofileserving.service.util.CommonUtil.transformCMTRequest
import com.ctrip.ibu.userprofileserving.service.util.Constant
import com.ctrip.ibu.userprofileserving.service.util.Constant.LOCALE_EN
import com.ctrip.ibu.userprofileserving.service.util.Constant.LOCALE_JA
import com.ctrip.ibu.userprofileserving.service.util.Constant.LOCALE_KO
import com.ctrip.ibu.userprofileserving.service.util.Constant.LOCALE_TH
import com.ctrip.ibu.userprofileserving.service.util.Constant.LOCALE_ZH
import com.ctrip.ibu.userprofileserving.service.util.DateUtils
import com.ctrip.ibu.userprofileserving.service.util.JsonUtil
import com.ctrip.ibu.userprofileserving.service.util.OKHttpUtils
import com.ctrip.ibu.userprofileserving.service.util.RetryUtils
import com.ctrip.ibu.userprofileserving.soa.CMTRecommendDateItemType
import com.ctrip.ibu.userprofileserving.soa.CMTRecommendItemType
import com.ctrip.ibu.userprofileserving.soa.Filter
import com.ctrip.ibu.userprofileserving.soa.RouteResp
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope
import org.apache.commons.lang.StringUtils
import org.springframework.stereotype.Service
import org.springframework.util.CollectionUtils
import qunar.tc.qconfig.client.TypedConfig
import qunar.tc.qconfig.client.spring.QMapConfig
import kotlin.collections.component1
import kotlin.collections.component2
import kotlin.collections.forEach
import kotlin.collections.get
import kotlin.collections.iterator
import kotlin.collections.plus

@Service
class CMTInternalService(
    private val cmtHotelService: CMTHotelService,
    private val userProfileCMTRecommendService: UserProfileCMTRecommendService,
    private val userProfileCostTimeService: UserProfileCostTimeService
) {

    @QMapConfig(value = "common.properties")
    lateinit var commonConfig: CommonConfig

    companion object {
        private val logger = org.slf4j.LoggerFactory.getLogger(CMTInternalService::class.java)
        private const val MIN_HOTEL_COUNT = 3
    }

    // =================================================================================================================
    // ================================================= Public Methods ================================================
    // =================================================================================================================

    /**
     * Generate CMT description based on itinerary information
     * @param req The request containing itinerary information
     * @param requestId The request ID for cost time tracking
     * @return The generated description or empty string if no description is available
     */
    fun getCMTDescription(req: CMTRecReqDTO, requestId: String): String {
        val startTime = System.currentTimeMillis()
        var input: String? = null
        var output: String? = null
        return try {
            val body = JsonUtil.toJson(req)
            input = body
            logger.info("Generating CMT description for request body: $body, requestId: $requestId")

            val result = RetryUtils.executeWithRetryAndDefaultResponse(
                operation = "Get Description API Call (requestId: $requestId)",
                maxRetries = 3,
                initialDelayMs = 1000L,
                createDefaultResponse = { "" }
            ) {
                OKHttpUtils.post(
                    url = commonConfig.attractionSlb + Constant.CMT_DESCRIBE_PATH,
                    headers = mapOf(
                        "Content-Type" to "application/json",
                        "Accept" to "application/json"
                    ),
                    body = body
                )
            }

            output = result
            val description = JsonUtil.fromJson<Map<String, Any>>(result)
                ?.get("description")
                ?.let { descValue ->
                    when (descValue) {
                        is String -> descValue
                        else -> descValue.toString()
                    }
                }
                ?.takeIf { it.isNotBlank() }
                ?: StringUtils.EMPTY

            logger.info("Successfully generated CMT description for requestId: $requestId, length: ${description.length}")
            description

        } catch (e: Exception) {
            logger.error("Failed to generate CMT description for requestId $requestId: ${e.message}", e)
            StringUtils.EMPTY
        } finally {
            // Record cost time
            val costTime = System.currentTimeMillis() - startTime
            try {
                userProfileCostTimeService.saveCostTime(
                    UserProfileTimeCost(
                        uid = req.uid,
                        requestId = requestId,
                        costType = Constant.CMT_DESCRIPTION_COST_TYPE,
                        costTime = costTime,
                        input = input,
                        output = output
                    )
                )
                logger.debug("Saved CMT description cost time: ${costTime}ms for requestId: $requestId")
            } catch (e: Exception) {
                logger.error("Failed to save CMT description cost time for requestId $requestId: ${e.message}", e)
            }
        }
    }

    /**
     * Assemble CMT recommendation data based on workflow results
     *
     * @param requestId Unique request identifier
     * @param req Request containing user and itinerary information
     * @param mainResult Main workflow result containing product selection, hotel filters, attractions and recommendation reasons
     */
    suspend fun assembleCMTRecommendData(
        requestId: String,
        req: CMTRecReqDTO,
        mainResult: WorkflowResult
    ) {
        val uid = req.uid
        val locale = req.locale
        val productSelData = mainResult.productSelData
        val hotelFilterData = mainResult.hotelFilterData
        val attractionData = mainResult.attractionData
        val recReasonData = mainResult.recReasonData

        logger.info("Assembling CMT recommendation data for uid=$uid, requestId=$requestId")

        //1:find destination city id
        val destinationCityId = req.route.find { it.type == Constant.CMT_DESTINATION_CITY_KEY }?.information?.cityId
            ?: throw IllegalArgumentException("Destination city ID not found in request")

        //2: Extract existing product selections by order
        val existProductSelByOrderList = extractProductSelByOrderList(req)

        //3: Convert transport data to product selection data
        val convertTransportProductSel = convertTransportToProductSel(productSelData, attractionData)

        //4: Convert rent car data to product selection data
//        val convertRentCarProductSel = convertRentCarToProductSel(convertTransportProductSel)

        //5. Filter and remove productSelData
        val finalProductSel = filterAndRemoveProductSel(convertTransportProductSel, existProductSelByOrderList, destinationCityId)

        //6. Collect all hotel and activity items that need processing
        val hotelItems = collectHotelItems(finalProductSel)
        val activityItems = collectActivityItems(finalProductSel)
        val generalRecommendationItems = collectGeneralRecommendationItems(finalProductSel)

        //7. Process hotels in parallel (expensive operations)
        processHotelsInParallel(finalProductSel, hotelItems, hotelFilterData, attractionData, uid, requestId, req)

        //8. Process activities sequentially (no expensive operations)
        processActivitiesSequentially(activityItems, attractionData)

        //9. Process general recommendations for all recommendation items
        processGeneralRecommendationsSequentially(generalRecommendationItems, recReasonData, attractionData, locale)

        //10. Create final recommendation response
        val recommendResponse = createRecommendResponse(finalProductSel)

        val routeInfo = RouteResp().apply {
            req.route.forEach {
                if (Constant.CMT_DESTINATION_CITY_KEY == it.type) {
                    this.destinationCityId = it.information?.cityId
                }
                if (Constant.CMT_DEPARTURE_CITY_KEY == it.type) {
                    this.departureCityId = it.information?.cityId
                }
            }
        }

        // Save to database
        val recommendMap = mapOf(
            Constant.CMT_RECOMMEND_DATA_KEY to recommendResponse,
            Constant.CMT_ROUTE_INFO_KEY to routeInfo,
            Constant.CMT_WORKFLOW_DATA_KEY to mainResult
        )

        userProfileCMTRecommendService.saveUserProfileCMTRecommend(
            UserProfileCMTRecommend(
                uid,
                req.itineraryKey,
                requestId,
                JsonUtil.toJson(recommendMap),
                Constant.CODE_SUCCESS
            )
        )

        logger.info("Successfully assembled and saved CMT recommendation data for uid=$uid, requestId=$requestId")
    }

    /**
     * Assemble default CMT data when the main workflow fails or returns empty results.
     * This method relies on existing order information to construct a basic recommendation.
     *
     * @param requestId The unique identifier for the request.
     * @param req The original CMT recommendation request DTO.
     * @param mainResult The result from the main workflow, which may be empty or partial.
     */
    fun assembleCMTDefaultData(requestId: String, req: CMTRecReqDTO, mainResult: WorkflowResult) {
        logAssemblingDefaultData(req.uid, req.itineraryKey, requestId)

        val flattenedOrders = extractAndFlattenOrders(req)
        val groupedOrdersByDate = groupOrdersByDate(flattenedOrders, req.locale)
        val cmtData = buildCMTDataFromGroupedOrders(groupedOrdersByDate)

        saveDefaultRecommendationData(req, requestId, mainResult, cmtData)

        logSuccessfulAssembly(req.uid, requestId)
    }

    suspend fun refreshHotelProducts(
        requestId: String,
        req: CMTRecReqDTO,
        mainResult: WorkflowResult,
        recItemTypes: List<CMTRecommendDateItemType>
    ) {
        val uid = req.uid
        val productSelData = mainResult.productSelData
        val hotelFilterData = mainResult.hotelFilterData
        val attractionData = mainResult.attractionData

        //1:find destination city id
        val destinationCityId = req.route.find { it.type == Constant.CMT_DESTINATION_CITY_KEY }?.information?.cityId
            ?: throw IllegalArgumentException("Destination city ID not found in request")

        //2: Extract existing product selections by order
        val existProductSelByOrderList = extractProductSelByOrderList(req)

        //3: Convert transport data to product selection data
        val convertTransportProductSel = convertTransportToProductSel(productSelData, attractionData)

        //4: Convert rent car data to product selection data
//        val convertRentCarProductSel = convertRentCarToProductSel(convertTransportProductSel)

        //5: Filter and remove productSelData
        val finalProductSel = filterAndRemoveProductSel(convertTransportProductSel, existProductSelByOrderList, destinationCityId)

        //6: Collect all hotel and activity items that need processing
        val hotelItems = collectHotelItems(finalProductSel)

        processHotelsInParallelV2(recItemTypes, hotelItems, hotelFilterData, attractionData, requestId, req)

        val routeInfo = RouteResp().apply {
            req.route.forEach {
                if (Constant.CMT_DESTINATION_CITY_KEY == it.type) {
                    this.destinationCityId = it.information?.cityId
                }
                if (Constant.CMT_DEPARTURE_CITY_KEY == it.type) {
                    this.departureCityId = it.information?.cityId
                }
            }
        }
        // Save to database
        val recommendMap = mapOf(
            Constant.CMT_RECOMMEND_DATA_KEY to recItemTypes,
            Constant.CMT_ROUTE_INFO_KEY to routeInfo,
            Constant.CMT_WORKFLOW_DATA_KEY to mainResult
        )

        userProfileCMTRecommendService.saveUserProfileCMTRecommend(
            UserProfileCMTRecommend(
                uid,
                req.itineraryKey,
                requestId,
                JsonUtil.toJson(recommendMap),
                Constant.CODE_SUCCESS
            )
        )
        logger.info("Successfully refreshed hotel products for uid=$uid, requestId=$requestId")
    }

    // =================================================================================================================
    // =================================== Private Data Classes for Processing =========================================
    // =================================================================================================================

    /**
     * Data class to hold hotel item information for processing
     */
    private data class HotelItem(
        val iDay: Int,
        val iProd: Int,
        val parameters: Parameter,
        val date: String
    )

    /**
     * Data class to hold activity item information for processing
     */
    private data class ActivityItem(
        val parameters: Parameter,
        val cityId: Int?,
        val date: String
    )

    /**
     * Data class to hold general recommendation item information for processing
     */
    private data class GeneralRecommendationItem(
        val iDay: Int,
        val iProd: Int,
        val productType: String,
        val parameters: Parameter,
        val date: String,
        val cityId: Int?
    )

    // =================================================================================================================
    // =================================== Private Methods: Recommendation Assembly ====================================
    // =================================================================================================================

    /**
     * Process hotels in parallel using coroutines since they involve expensive getHotels operations
     */
    private suspend fun processHotelsInParallel(
        finalProductSel: List<PrdTypeRecDTO>,
        hotelItems: List<HotelItem>,
        hotelFilterData: Map<Int, Parameter>,
        attractionData: AttractionResult?,
        uid: String,
        requestId: String,
        req: CMTRecReqDTO
    ) {
        if (hotelItems.isEmpty()) {
            logger.info("No hotel items to process for requestId=$requestId")
            return
        }

        val startTime = System.currentTimeMillis()
        var input: String? = null
        var output: String? = null

        try {
            // Prepare input data for monitoring
            input = "hotelItems=${hotelItems.size}, uid=$uid, requestId=$requestId"

            logger.info("Processing ${hotelItems.size} hotel items in parallel using coroutines for requestId=$requestId")

            // Use coroutines to process hotels concurrently
            coroutineScope {
                hotelItems.map { hotelItem ->
                    async {
                        try {
                            processHotelRecommendation(
                                iDay = hotelItem.iDay,
                                parameters = hotelItem.parameters,
                                hotelFilterData = hotelFilterData,
                                attractionData = attractionData,
                                productSelData = finalProductSel.find { it.date == hotelItem.date },
                                date = hotelItem.date,
                                uid = uid,
                                requestId = requestId,
                                req
                            )
                        } catch (e: Exception) {
                            logger.error("Failed to process hotel item for day=${hotelItem.iDay}, prod=${hotelItem.iProd}, requestId=$requestId", e)
                        }
                    }
                }.awaitAll()
            }

            // Prepare output data for monitoring
            output = "Successfully processed ${hotelItems.size} hotel items in parallel"

            val totalTime = System.currentTimeMillis() - startTime
            logger.info("Completed parallel processing of ${hotelItems.size} hotel items using coroutines for requestId=$requestId, totalTime=${totalTime}ms")

        } finally {
            // Record cost time for performance monitoring
            val costTime = System.currentTimeMillis() - startTime
            try {
                userProfileCostTimeService.saveCostTime(
                    UserProfileTimeCost(
                        uid = uid,
                        requestId = requestId,
                        costType = "hotel_parallel_processing",
                        costTime = costTime,
                        input = input,
                        output = output
                    )
                )
                logger.debug("Saved hotel parallel processing cost time: ${costTime}ms for requestId: $requestId")
            } catch (e: Exception) {
                logger.error("Failed to save hotel parallel processing cost time for requestId $requestId: ${e.message}", e)
            }
        }
    }

    private suspend fun processHotelsInParallelV2(
        recItemTypes: List<CMTRecommendDateItemType>,
        hotelItems: List<HotelItem>,
        hotelFilterData: Map<Int, Parameter>,
        attractionData: AttractionResult?,
        requestId: String,
        req: CMTRecReqDTO
    ) {
        if (hotelItems.isEmpty()) {
            logger.info("No hotel items to process hotel in parallel v2 for requestId=$requestId")
            return
        }

        val startTime = System.currentTimeMillis()
        var input: String? = null
        var output: String? = null

        try {
            // Prepare input data for monitoring
            input = "hotelItems=${hotelItems.size}, uid=${req.uid}, requestId=$requestId"

            logger.info("Processing v2 ${hotelItems.size} hotel items in parallel using coroutines for requestId=$requestId")

            // Use coroutines to process hotels concurrently
            coroutineScope {
                hotelItems.map { hotelItem ->
                    async {
                        try {
                            processHotelRecommendationV2(
                                iDay = hotelItem.iDay,
                                parameters = hotelItem.parameters,
                                hotelFilterData = hotelFilterData,
                                attractionData = attractionData,
                                recItemType = recItemTypes.find { it.date == hotelItem.date },
                                date = hotelItem.date,
                                uid = req.uid,
                                requestId = requestId,
                                req
                            )
                        } catch (e: Exception) {
                            logger.error(
                                "Failed to process in parallel v2 hotel item for day=${hotelItem.iDay}, prod=${hotelItem.iProd}, requestId=$requestId",
                                e
                            )
                        }
                    }
                }.awaitAll()
            }

            // Prepare output data for monitoring
            output = "Successfully processed ${hotelItems.size} hotel items in parallel"

            val totalTime = System.currentTimeMillis() - startTime
            logger.info("Completed parallel v2 processing of ${hotelItems.size} hotel items using coroutines for requestId=$requestId, totalTime=${totalTime}ms")

        } finally {
            // Record cost time for performance monitoring
            val costTime = System.currentTimeMillis() - startTime
            try {
                userProfileCostTimeService.saveCostTime(
                    UserProfileTimeCost(
                        uid = req.uid,
                        requestId = requestId,
                        costType = "hotel_parallel_processing",
                        costTime = costTime,
                        input = input,
                        output = output
                    )
                )
                logger.debug("Saved hotel parallel v2 processing cost time: ${costTime}ms for requestId: $requestId")
            } catch (e: Exception) {
                logger.error(
                    "Failed to save hotel parallel v2 processing cost time for requestId $requestId: ${e.message}",
                    e
                )
            }
        }
    }

    /**
     * Process activities sequentially since they don't involve expensive operations
     */
    private fun processActivitiesSequentially(
        activityItems: List<ActivityItem>,
        attractionData: AttractionResult?
    ) {
        if (activityItems.isEmpty()) {
            logger.debug("No activity items to process")
            return
        }

        logger.info("Processing ${activityItems.size} activity items sequentially")

        activityItems.forEach { activityItem ->
            try {
                processAttractionRecommendation(
                    parameters = activityItem.parameters,
                    attractionData = attractionData,
                    cityId = activityItem.cityId,
                    date = activityItem.date
                )
            } catch (e: Exception) {
                logger.error("Failed to process activity item for cityId=${activityItem.cityId}, date=${activityItem.date}", e)
            }
        }

        logger.info("Completed sequential processing of ${activityItems.size} activity items")
    }

    /**
     * Process general recommendations sequentially for all recommendation items
     */
    private fun processGeneralRecommendationsSequentially(
        generalRecommendationItems: List<GeneralRecommendationItem>,
        recReasonData: List<RecReasonDTO>,
        attractionData: AttractionResult?,
        locale: String
    ) {
        if (generalRecommendationItems.isEmpty()) {
            logger.debug("No general recommendation items to process")
            return
        }

        logger.info("Processing ${generalRecommendationItems.size} general recommendation items sequentially")

        generalRecommendationItems.forEach { item ->
            try {
                processGeneralRecommendation(
                    iDay = item.iDay,
                    iProd = item.iProd,
                    productType = item.productType,
                    parameters = item.parameters,
                    recReasonData = recReasonData,
                    attractionData = attractionData,
                    date = item.date,
                    cityId = item.cityId,
                    locale = locale
                )
            } catch (e: Exception) {
                logger.error("Failed to process general recommendation for day=${item.iDay}, prod=${item.iProd}, productType=${item.productType}", e)
            }
        }

        logger.info("Completed sequential processing of ${generalRecommendationItems.size} general recommendation items")
    }

    // =================================================================================================================
    // =================================== Private Methods: Data Extraction & Filtering ================================
    // =================================================================================================================

    /**
     * Extracts existing product selections from the request's order list.
     * This is used to compare against AI-generated recommendations.
     */
    private fun extractProductSelByOrderList(req: CMTRecReqDTO): List<PrdTypeRecDTO> {
        val mutablePrdType = mutableListOf<PrdTypeRecDTO>()
        req.orderList.forEach { order ->
            if (!CollectionUtils.isEmpty(order.flightOrders)) {
                order.flightOrders.forEachIndexed { flightIndex, flightOrder ->
                    val productType = flightOrder.productType
                    val date = flightOrder.information.departureTime.split(" ")[0]
                    val departureTime = flightOrder.information.departureTime.split(" ")[1]
                    val arrivalTime = flightOrder.information.arrivalTime.split(" ")[1]
                    val prdTypeRecDTO = PrdTypeRecDTO(
                        dayId = flightIndex,
                        date = date,
                        existingOrders = "",
                        requirementAnalysis = "",
                        dailySchedule = listOf(
                            DailyScheduleItem(
                                productType = productType,
                                status = Constant.CMT_ORDER_EXIST_STATUS,
                                parameters = Parameter(
                                    departureTime = departureTime,
                                    arrivalTime = arrivalTime,
                                )
                            )
                        )
                    )
                    mutablePrdType.add(prdTypeRecDTO)
                }
            }
            if (order.airportOrder != null) {
                val productType = order.airportOrder.productType
                val date = order.airportOrder.information.time.split(" ")[0]
                val prdTypeRecDTO = PrdTypeRecDTO(
                    dayId = 0,
                    date = date,
                    existingOrders = "",
                    requirementAnalysis = "",
                    dailySchedule = listOf(
                        DailyScheduleItem(
                            productType = productType,
                            status = Constant.CMT_ORDER_EXIST_STATUS,
                            parameters = Parameter(
                                description = ""
                            )
                        )
                    )
                )
                mutablePrdType.add(prdTypeRecDTO)
            }
            if (order.hotelOrder != null) {
                val date = order.hotelOrder.information.checkinDate.split(" ")[0]
                val prdTypeRecDTO = PrdTypeRecDTO(
                    dayId = 0,
                    date = date,
                    existingOrders = "",
                    requirementAnalysis = "",
                    dailySchedule = listOf(
                        DailyScheduleItem(
                            productType = CMTProductType.HOTEL.value,
                            status = Constant.CMT_ORDER_EXIST_STATUS,
                            parameters = Parameter(
                                checkin = order.hotelOrder.information.checkinDate,
                                checkout = order.hotelOrder.information.checkoutDate,
                                cityId = order.hotelOrder.information.hotelCityId.toInt(),
                                cityName = order.hotelOrder.information.hotelCityNameEn,
                            )
                        )
                    )
                )
                mutablePrdType.add(prdTypeRecDTO)
            }
            if (order.carOrder != null) {
                val date = order.carOrder.information.pickUpTime.split(" ")[0]
                val returnDate = order.carOrder.information.dropOffTime.split(" ")[0]
                val diff = DateUtils.getDiffDays(date, returnDate)
                for (i in 0..diff) {
                    val currentDate = DateUtils.getDateAfterDays(date, i.toInt()) ?: date
                    val prdTypeRecDTO = PrdTypeRecDTO(
                        dayId = i.toInt(),
                        date = currentDate,
                        existingOrders = "",
                        requirementAnalysis = "",
                        dailySchedule = listOf(
                            DailyScheduleItem(
                                productType = CMTProductType.RENT_CAR.value,
                                status = Constant.CMT_ORDER_EXIST_STATUS,
                                parameters = Parameter(
                                    cityId = order.carOrder.information.cityId.toInt(),
                                    cityName = order.carOrder.information.cityNameEn,
                                    pickUpDate = date,
                                    returnDate = returnDate
                                )
                            )
                        )
                    )
                    mutablePrdType.add(prdTypeRecDTO)
                }
            }
            if (order.tntOrder != null) {
                val date = order.tntOrder.information.date.split(" ")[0]
                val productType = order.tntOrder.productType
                val prdTypeRecDTO = PrdTypeRecDTO(
                    dayId = 0,
                    date = date,
                    existingOrders = "",
                    requirementAnalysis = "",
                    dailySchedule = listOf(
                        DailyScheduleItem(
                            productType = productType,
                            status = Constant.CMT_ORDER_EXIST_STATUS,
                            parameters = Parameter(
                                cityId = order.tntOrder.information.cityId.toInt(),
                                cityName = order.tntOrder.information.cityNameEn,
                            )
                        )
                    )
                )
                mutablePrdType.add(prdTypeRecDTO)
            }
            if (order.trainOrder != null) {
                val productType = order.trainOrder.productType
                val date = order.trainOrder.information.departureTime.split(" ")[0]
                val departureTime = order.trainOrder.information.departureTime.split(" ")[1]
                val arrivalTime = order.trainOrder.information.arrivalTime.split(" ")[1]
                val prdTypeRecDTO = PrdTypeRecDTO(
                    dayId = 0,
                    date = date,
                    existingOrders = "",
                    requirementAnalysis = "",
                    dailySchedule = listOf(
                        DailyScheduleItem(
                            productType = productType,
                            status = Constant.CMT_ORDER_EXIST_STATUS,
                            parameters = Parameter(
                                departureTime = departureTime,
                                arrivalTime = arrivalTime,
                            )
                        )
                    )
                )
                mutablePrdType.add(prdTypeRecDTO)
            }
        }

        // Sort and group by date
        return groupAndSortByDate(mutablePrdType)
    }

    /**
     * Sort and group PrdTypeRecDTO by date
     * Data for the same date will be merged into one PrdTypeRecDTO, with dailySchedule containing all products for that date
     * dayId will be reallocated based on the date sorting result
     */
    private fun groupAndSortByDate(prdTypeList: List<PrdTypeRecDTO>): List<PrdTypeRecDTO> {
        // Group by date
        val groupedByDate = prdTypeList.groupBy { it.date }

        // Sort by date
        val sortedDates = groupedByDate.keys.sortedBy { dateString ->
            // Convert date string to a comparable format
            try {
                // Supports yyyy-MM-dd and yyyy/MM/dd formats
                val normalizedDate = dateString.replace("/", "-")
                java.time.LocalDate.parse(normalizedDate)
            } catch (e: Exception) {
                // If date parsing fails, use string sorting as a fallback
                java.time.LocalDate.MIN
            }
        }

        // Rebuild the result list
        return sortedDates.mapIndexed { dayIndex, date ->
            val itemsForDate = groupedByDate[date]!!

            // Merge all dailySchedules for the same date
            val mergedDailySchedule = itemsForDate.flatMap { it.dailySchedule }

            // Use the first item as a base to update dayId and dailySchedule
            val firstItem = itemsForDate.first()
            PrdTypeRecDTO(
                dayId = dayIndex + 1, // dayId starts from 1
                date = date,
                existingOrders = firstItem.existingOrders,
                requirementAnalysis = firstItem.requirementAnalysis,
                dailySchedule = mergedDailySchedule
            )
        }
    }

    /**
     * Convert transport data to product selection data
     * This method is used to convert AI-generated transport data into a format compatible with product selection.
     */
    /**
     * Insert TRANSPORT product line into productSelData based on attractionData
     * 根据日期往原有的 productSelData 当中插入一个 TRANSPORT 的产线
     *
     * @param productSelData 原有的产品选择数据
     * @param attractionData 景点数据，包含交通建议
     * @return 插入 TRANSPORT 产线后的产品选择数据
     */
    private fun convertTransportToProductSel(
        productSelData: List<PrdTypeRecDTO>,
        attractionData: AttractionResult?
    ): List<PrdTypeRecDTO> {
        if (attractionData == null || attractionData.cities.isEmpty()) {
            logger.info("AttractionData is null or empty, no transport data to insert")
            return productSelData
        }

        val mutableProductSelData = productSelData.toMutableList()

        // 遍历 attractionData 中的所有城市
        attractionData.cities.forEach { (cityId, cityData) ->
            logger.debug("Processing city: ${cityData.cityName} (ID: $cityId)")

            // 遍历城市中的每日数据
            cityData.dailyData.forEach { (date, dailyData) ->
                // 检查是否有交通建议
                val transportationText = dailyData.transportation
                if (!transportationText.isNullOrBlank()) {
                    logger.debug("Found transportation data for date: $date, city: ${cityData.cityName}")

                    // 在 productSelData 中找到对应日期的记录
                    val matchingPrdTypeIndex = mutableProductSelData.indexOfFirst { prdType ->
                        prdType.date == date
                    }

                    if (matchingPrdTypeIndex != -1) {
                        val existingPrdType = mutableProductSelData[matchingPrdTypeIndex]

                        // 检查该日期是否已经存在 TRANSPORT 类型的产品
                        val hasTransportProduct = existingPrdType.dailySchedule.any { item ->
                            item.productType == CMTProductType.TRANSPORT.value
                        }

                        if (!hasTransportProduct) {
                            // 创建新的 TRANSPORT DailyScheduleItem
                            val transportItem = DailyScheduleItem(
                                productType = CMTProductType.TRANSPORT.value,
                                status = Constant.CMT_RECOMMENDATION_STATUS,
                                parameters = Parameter(
                                    recommendText = transportationText,
                                    cityId = cityId,
                                    cityName = cityData.cityName
                                )
                            )

                            // 创建新的 dailySchedule 列表，添加 TRANSPORT 项
                            val updatedDailySchedule = existingPrdType.dailySchedule.toMutableList()
                            updatedDailySchedule.add(transportItem)

                            // 创建更新后的 PrdTypeRecDTO
                            val updatedPrdType = existingPrdType.copy(
                                dailySchedule = updatedDailySchedule
                            )

                            // 替换原有记录
                            mutableProductSelData[matchingPrdTypeIndex] = updatedPrdType

                            logger.info("Successfully inserted TRANSPORT product for date: $date, city: ${cityData.cityName}")
                        } else {
                            logger.debug("TRANSPORT product already exists for date: $date, skipping insertion")
                        }
                    } else {
                        logger.warn("No matching PrdTypeRecDTO found for date: $date in productSelData")
                    }
                } else {
                    logger.debug("No transportation data found for date: $date, city: ${cityData.cityName}")
                }
            }
        }

        return mutableProductSelData
    }
    
    /**
     * Convert rent-a-car data to product selection data
     */
    private fun convertRentCarToProductSel(productSelData: List<PrdTypeRecDTO>): List<PrdTypeRecDTO> {
        // Step 1: Find all recommended rent-a-car periods.
        val rentCarPeriods = productSelData.flatMap { it.dailySchedule }
            .filter { it.productType == CMTProductType.RENT_CAR.value && it.status == Constant.CMT_RECOMMENDATION_STATUS }
            .mapNotNull {
                val pickUp = it.parameters.pickUpDate
                val returnDate = it.parameters.returnDate
                if (pickUp != null && returnDate != null) {
                    Pair(pickUp, returnDate)
                } else {
                    null
                }
            }

        if (rentCarPeriods.isEmpty()) {
            return productSelData
        }

        // Step 2: Iterate through all days and update the status of RENT_CAR items within the periods.
        return productSelData.map { dayData ->
            val currentDate = dayData.date
            val isInRentCarPeriod = rentCarPeriods.any { (start, end) ->
                currentDate >= start && currentDate <= end
            }

            if (isInRentCarPeriod) {
                val updatedDailySchedule = dayData.dailySchedule.map { scheduleItem ->
                    if (scheduleItem.productType == CMTProductType.RENT_CAR.value) {
                        scheduleItem.copy(status = Constant.CMT_RECOMMENDATION_STATUS)
                    } else {
                        scheduleItem
                    }
                }
                dayData.copy(dailySchedule = updatedDailySchedule)
            } else {
                dayData
            }
        }
    }

    /**
     * Filters the AI-generated product selection data against existing orders and applies business rules.
     */
    private fun filterAndRemoveProductSel(
        productSelData: List<PrdTypeRecDTO>,
        existProductSelByOrderList: List<PrdTypeRecDTO>,
        destinationCityId: Int
    ): List<PrdTypeRecDTO> {
        return productSelData.map { dayData ->
            val existDailySchedule = existProductSelByOrderList.firstOrNull { it.date == dayData.date }?.dailySchedule ?: emptyList()
            val filteredSchedule = filterDailySchedule(dayData.dailySchedule, existDailySchedule, destinationCityId)
            dayData.copy(dailySchedule = filteredSchedule)
        }
    }

    /**
     * Filter daily schedule items based on status and product type rules
     */
    private fun filterDailySchedule(
        dailySchedule: List<DailyScheduleItem>,
        existDailySchedule: List<DailyScheduleItem>,
        destinationCityId: Int
    ): List<DailyScheduleItem> {
        // Step 1: Filter out items with in_use and end_use status
        val statusFiltered = dailySchedule.filter { item ->
            val status = item.status.lowercase()
            status != Constant.CMT_IN_USE_STATUS && status != Constant.CMT_END_USE_STATUS
        }

        // Step 2: Apply product type positioning rules
        return applyProductTypeRules(statusFiltered, existDailySchedule, destinationCityId)
    }

    /**
     * Apply product type positioning and quantity rules
     */
    private fun applyProductTypeRules(
        items: List<DailyScheduleItem>,
        existDailySchedule: List<DailyScheduleItem>,
        destinationCityId: Int
    ): List<DailyScheduleItem> {
        // Step 1: Compare items and existDailySchedule, and add missing CMT_ORDER_EXIST_STATUS items
        val mergedItems = mergeWithExistingOrders(items, existDailySchedule)


        // Step 2: Remove non-existent CMT_ORDER_EXIST_STATUS items based on existDailySchedule
        val validatedItems = removeInvalidOrderExistItems(mergedItems, existDailySchedule)

        // Step 3: Check the cityId for HOTEL/ACTIVITY types and replace it with destinationCityId
        val cityUpdatedItems = updateHotelAndActivityCityId(validatedItems, destinationCityId)

        // Step 4: Apply sorting and filtering rules
        return applySortingAndFilteringRules(cityUpdatedItems)
    }

    /**
     * Compare items and existDailySchedule, and add missing CMT_ORDER_EXIST_STATUS items
     */
    private fun mergeWithExistingOrders(
        items: List<DailyScheduleItem>,
        existDailySchedule: List<DailyScheduleItem>
    ): List<DailyScheduleItem> {
        val result = items.toMutableList()

        // Get all CMT_ORDER_EXIST_STATUS items from items
        val existingOrderItems = items.filter { it.status == Constant.CMT_ORDER_EXIST_STATUS }

        // Get all CMT_ORDER_EXIST_STATUS items from existDailySchedule
        val existDailyOrderItems = existDailySchedule.filter { it.status == Constant.CMT_ORDER_EXIST_STATUS }

        // Compare and find missing items
        existDailyOrderItems.forEach { existItem ->
            val isAlreadyPresent = existingOrderItems.any { currentItem ->
                currentItem.productType == existItem.productType &&
                        currentItem.status == existItem.status
            }

            if (!isAlreadyPresent) {
                result.add(existItem)
            }
        }

        return result
    }

    /**
     * Remove non-existent CMT_ORDER_EXIST_STATUS items based on existDailySchedule
     */
    private fun removeInvalidOrderExistItems(
        items: List<DailyScheduleItem>,
        existDailySchedule: List<DailyScheduleItem>
    ): List<DailyScheduleItem> {
        // Get all CMT_ORDER_EXIST_STATUS items from existDailySchedule as a baseline
        val existDailyOrderItems = existDailySchedule.filter { it.status == Constant.CMT_ORDER_EXIST_STATUS }

        // Filter items, keeping non-CMT_ORDER_EXIST_STATUS items and CMT_ORDER_EXIST_STATUS items that exist in the baseline
        return items.filter { item ->
            if (item.status == Constant.CMT_ORDER_EXIST_STATUS) {
                // For CMT_ORDER_EXIST_STATUS items, a matching item must be found in existDailyOrderItems
                existDailyOrderItems.any { existItem ->
                    existItem.productType == item.productType &&
                            existItem.status == item.status
                }
            } else {
                // Non-CMT_ORDER_EXIST_STATUS items are kept directly
                true
            }
        }
    }

    /**
     * Check cityId for HOTEL type and replace with destinationCityId
     */
    private fun updateHotelAndActivityCityId(
        items: List<DailyScheduleItem>,
        destinationCityId: Int
    ): List<DailyScheduleItem> {
        return items.map { item ->
            if ((item.productType == CMTProductType.HOTEL.value || item.productType == CMTProductType.ACTIVITY.value) &&
                item.parameters.cityId != destinationCityId
            ) {
                // Create a new Parameter object and update cityId
                val updatedParameters = item.parameters.copy(cityId = destinationCityId)
                item.copy(parameters = updatedParameters)
            } else {
                item
            }
        }
    }

    /**
     * Apply new sorting and filtering rules
     */
    private fun applySortingAndFilteringRules(items: List<DailyScheduleItem>): List<DailyScheduleItem> {
        // Step 1: Apply transportation filtering logic
        val filteredItems = applyTransportationFiltering(items)

        // Step 2: Apply sorting logic
        return applySortingLogic(filteredItems)
    }

    /**
     * First method: Transportation filtering logic
     * Filter transportation items (AIRPORT_PICKUP, AIRPORT_DROPOFF, RENT_CAR, TRANSPORT)
     * Rules:
     * 1. Check if any of these four types have existing orders, if yes, return that day's transportation item directly
     * 2. If none of these four types have existing orders, return by priority:
     *    1. RENT_CAR
     *    2. AIRPORT_PICKUP/AIRPORT_DROPOFF
     *    3. TRANSPORT
     */
    private fun applyTransportationFiltering(items: List<DailyScheduleItem>): List<DailyScheduleItem> {
        val result = mutableListOf<DailyScheduleItem>()

        // Get all non-transportation items (keep as is)
        val nonTransportationItems = items.filter {
            it.productType != CMTProductType.AIRPORT_PICKUP.value &&
            it.productType != CMTProductType.AIRPORT_DROPOFF.value &&
            it.productType != CMTProductType.RENT_CAR.value &&
            it.productType != CMTProductType.TRANSPORT.value
        }
        result.addAll(nonTransportationItems)

        // Get all transportation items
        val airportPickupItems = items.filter { it.productType == CMTProductType.AIRPORT_PICKUP.value }
        val airportDropOffItems = items.filter { it.productType == CMTProductType.AIRPORT_DROPOFF.value }
        val rentCarItems = items.filter { it.productType == CMTProductType.RENT_CAR.value }
        val transportItems = items.filter { it.productType == CMTProductType.TRANSPORT.value }

        // Check if any transportation type has existing orders
        val existingTransportationItems = (airportPickupItems + airportDropOffItems + rentCarItems + transportItems)
            .filter { it.status == Constant.CMT_ORDER_EXIST_STATUS }

        if (existingTransportationItems.isNotEmpty()) {
            // If there are existing orders, add all existing transportation items
            result.addAll(existingTransportationItems)
        } else {
            // If no existing orders, apply priority logic
            when {
                // Priority 1: RENT_CAR
                rentCarItems.isNotEmpty() -> {
                    result.add(rentCarItems.first())
                }
                // Priority 2: AIRPORT_PICKUP/AIRPORT_DROPOFF
                airportPickupItems.isNotEmpty() || airportDropOffItems.isNotEmpty() -> {
                    airportPickupItems.firstOrNull()?.let { result.add(it) }
                    airportDropOffItems.firstOrNull()?.let { result.add(it) }
                }
                // Priority 3: TRANSPORT
                transportItems.isNotEmpty() -> {
                    result.add(transportItems.first())
                }
            }
        }

        return result
    }

    /**
     * Second method: Sorting logic
     * Apply sorting rules to all items (including filtered transportation items)
     */
    private fun applySortingLogic(items: List<DailyScheduleItem>): List<DailyScheduleItem> {
        val result = mutableListOf<DailyScheduleItem>()

        // Categorize all items
        val departItems = items.filter {
            it.productType == CMTProductType.DEPART_FLIGHT.value ||
                    it.productType == CMTProductType.DEPART_TRAIN.value
        }
        val returnItems = items.filter {
            it.productType == CMTProductType.RETURN_FLIGHT.value ||
                    it.productType == CMTProductType.RETURN_TRAIN.value
        }
        val airportPickupItems = items.filter { it.productType == CMTProductType.AIRPORT_PICKUP.value }
        val airportDropOffItems = items.filter { it.productType == CMTProductType.AIRPORT_DROPOFF.value }
        val rentCarItems = items.filter { it.productType == CMTProductType.RENT_CAR.value }
        val transportItems = items.filter { it.productType == CMTProductType.TRANSPORT.value }
        val activityItems = items.filter { it.productType == CMTProductType.ACTIVITY.value }
        val existActivityItems = items.filter { it.status == Constant.CMT_ORDER_EXIST_STATUS && it.productType == CMTProductType.ACTIVITY.value }
        val hotelItems = items.filter { it.productType == CMTProductType.HOTEL.value }

        // 1. DEPART_FLIGHT/DEPART_TRAIN comes first
        result.addAll(departItems)

        // 2. AIRPORT_PICKUP comes second
        result.addAll(airportPickupItems)

        // 3. Hotel items (no conflict handling needed - applyProductTypeRules already ensures no conflicts)
        result.addAll(hotelItems)

        // 4. Add RENT_CAR and TRANSPORT items (already filtered by transportation filtering logic)
        result.addAll(rentCarItems)

        // If there are transport items but no activity items, add the first transport item
        if (transportItems.isNotEmpty() && existActivityItems.isEmpty()) {
            result.add(transportItems.first())
        }

        // 5. ACTIVITY items come next, only one activity is allowed per day
        if (activityItems.isNotEmpty()) {
            result.add(activityItems.first()) // Take only the first activity
        }

        // 6. AIRPORT_DROPOFF comes before RETURN_FLIGHT/RETURN_TRAIN
        result.addAll(airportDropOffItems)

        // 7. RETURN_FLIGHT/RETURN_TRAIN comes last
        result.addAll(returnItems)

        return result
    }

    // =================================================================================================================
    // =================================== Private Methods: Recommendation Processing ==================================
    // =================================================================================================================

    /**
     * Process hotel recommendation by applying filters and retrieving hotel options
     */
    private fun processHotelRecommendation(
        iDay: Int,
        parameters: Parameter,
        hotelFilterData: Map<Int, Parameter>,
        attractionData: AttractionResult?,
        productSelData: PrdTypeRecDTO?,
        date: String,
        uid: String,
        requestId: String,
        req: CMTRecReqDTO
    ) {
        // Extract UBT-based hotel filters
        val ubtFilters = hotelFilterData[iDay]?.hotelFilters ?: emptyList()

        // Extract POI-based hotel filters from attraction data if activity exists
        val combinedFilters = if (productSelData?.dailySchedule?.any { it.productType == CMTProductType.ACTIVITY.value } == true) {
            extractPoiFilters(attractionData, parameters.cityId, date, ubtFilters)
        } else {
            ubtFilters
        }

        // Update parameters with combined filters
        parameters.hotelFilters = combinedFilters

        // Get hotels using intersection approach with combined filters
        val hotelIds = getHotelsWithFallback(
            uid = uid,
            cityId = parameters.cityId ?: 0,
            checkIn = parameters.checkin.orEmpty(),
            checkOut = parameters.checkout.orEmpty(),
            clientId = req.head.clientId,
            filterIds = combinedFilters,
            req,
            requestId = requestId,
        )

        parameters.hotelIds = hotelIds
    }

    private fun processHotelRecommendationV2(
        iDay: Int,
        parameters: Parameter,
        hotelFilterData: Map<Int, Parameter>,
        attractionData: AttractionResult?,
        recItemType: CMTRecommendDateItemType?,
        date: String,
        uid: String,
        requestId: String,
        req: CMTRecReqDTO
    ) {
        // Extract UBT-based hotel filters
        val ubtFilters = hotelFilterData[iDay]?.hotelFilters ?: emptyList()

        // Extract POI-based hotel filters from attraction data if activity exists
        val combinedFilters = if (recItemType?.recommendList?.any { it.productType == CMTProductType.ACTIVITY.value } == true) {
            extractPoiFilters(attractionData, parameters.cityId, date, ubtFilters)
        } else {
            ubtFilters
        }

        // Update parameters with combined filters
        parameters.hotelFilters = combinedFilters

        // Get hotels using intersection approach with combined filters
        val hotelIds = getHotelsWithFallback(
            uid = uid,
            cityId = parameters.cityId ?: 0,
            checkIn = parameters.checkin.orEmpty(),
            checkOut = parameters.checkout.orEmpty(),
            clientId = req.head.clientId,
            filterIds = combinedFilters,
            req,
            requestId = requestId,
        )

        recItemType?.recommendList?.forEach { item ->
            if (item.productType == CMTProductType.HOTEL.value) {
                item.productIdList = hotelIds
            }
        }
    }

    /**
     * Process attraction recommendation by setting attraction IDs and names
     */
    private fun processAttractionRecommendation(
        parameters: Parameter,
        attractionData: AttractionResult?,
        cityId: Int?,
        date: String
    ) {
        attractionData?.let { attrData ->
            parameters.attractionsId = attrData.cities[cityId]?.dailyData?.get(date)?.attractionIds
            parameters.attractionsName = attrData.cities[cityId]?.dailyData?.get(date)?.attractionNames
        }
    }

    /**
     * Process general recommendation by setting recommendation text
     */
    private fun processGeneralRecommendation(
        iDay: Int,
        iProd: Int,
        productType: String,
        parameters: Parameter,
        recReasonData: List<RecReasonDTO>,
        attractionData: AttractionResult?,
        date: String,
        cityId: Int?,
        locale: String
    ) {
        val (recommendText, initialRoute) = extractRecommendationText(
            productType = productType,
            attractionData = attractionData,
            cityId = cityId,
            date = date,
            recReasonData = recReasonData,
            iDay = iDay,
            iProd = iProd
        )

        val (finalText, usedDefault) = checkRecommendText(recommendText, productType, locale)
        val finalRoute = if (usedDefault) Constant.CMT_RECOMMEND_TEXT_ROUTE_DEFAULT else initialRoute

        parameters.apply {
            this.recommendText = finalText
            this.recommendTextRoute = finalRoute
        }
    }

    // =================================================================================================================
    // =================================== Private Methods: Hotel Recommendation Helpers ===============================
    // =================================================================================================================

    /**
     * Collect all hotel items that need processing from productSelData
     */
    private fun collectHotelItems(productSelData: List<PrdTypeRecDTO>): List<HotelItem> {
        return productSelData.flatMapIndexed { iDay, productSel ->
            val date = productSel.date.replace("/", "-")
            productSel.dailySchedule.mapIndexedNotNull { iProd, dailyScheduleItem ->
                val productType = dailyScheduleItem.productType.lowercase()
                val status = dailyScheduleItem.status.lowercase()

                if (productType == CMTProductType.HOTEL.value.lowercase()
                    && status == Constant.CMT_RECOMMENDATION_STATUS
                ) {
                    HotelItem(iDay, iProd, dailyScheduleItem.parameters, date)
                } else null
            }
        }
    }

    /**
     * Get hotels with intersection approach and fallback to default filters
     */
    private fun getHotelsWithFallback(
        uid: String,
        cityId: Int,
        checkIn: String,
        checkOut: String,
        clientId: String,
        filterIds: List<HotelFilterDTO>,
        req: CMTRecReqDTO,
        requestId: String
    ): List<Int> {
        val request = CMTHotelRequest(
            uid = uid,
            cityID = cityId,
            checkIn = checkIn,
            checkOut = checkOut,
            adultNum = req.travelers.adultCount,
            childNum = req.travelers.childCount,
            clientId = clientId,
            requestId = requestId,
            filters = filterIds,
            traceId = req.context.traceId,
            head = req.head
        )

        // Try with filters first
        val filteredResponse = cmtHotelService.getHotels(request)
        if (filteredResponse.hotels.size > MIN_HOTEL_COUNT) {
            return filteredResponse.hotels
        }

        // Fallback: supplement with unfiltered results
        val defaultResponse = cmtHotelService.getHotels(request.copy(filters = emptyList()))
        return combineHotelResults(filteredResponse.hotels, defaultResponse.hotels)
    }

    /**
     * Combine filtered and default hotel results, prioritizing filtered results
     */
    private fun combineHotelResults(filteredHotels: List<Int>, defaultHotels: List<Int>): List<Int> {
        val filteredSet = filteredHotels.toSet()
        val supplementaryHotels = defaultHotels
            .filterNot { it in filteredSet }
            .take(maxOf(0, MIN_HOTEL_COUNT + 1 - filteredHotels.size))

        return filteredHotels + supplementaryHotels
    }

    /**
     * Extract POI-based hotel filters from attraction data
     */
    private fun extractPoiFilters(
        attractionData: AttractionResult?,
        cityId: Int?,
        date: String,
        ubtFilters: List<HotelFilterDTO>
    ): List<HotelFilterDTO> {
        val dailyData = attractionData?.cities?.get(cityId)?.dailyData?.get(date)
        val poiFilters = dailyData?.hotelFilterDTO
        poiFilters?.let {
            val combinedFilters = ubtFilters + poiFilters
            return combinedFilters
        }
        return ubtFilters
    }

    /**
     * Find hotel records from all product data
     *
     * @param productSelData All product selection data
     * @return List of hotel records
     */
    private fun findHotelRecords(productSelData: List<PrdTypeRecDTO>): List<DailyScheduleItem> {
        return productSelData.flatMap { dayData ->
            dayData.dailySchedule.filter { item ->
                item.productType == CMTProductType.HOTEL.value &&
                        item.parameters.checkin != null &&
                        item.parameters.checkout != null
            }
        }
    }

    private fun findOrderExistHotelRecords(productSelData: List<PrdTypeRecDTO>): List<DailyScheduleItem> {
        return productSelData.flatMap { dayData ->
            dayData.dailySchedule.filter { item ->
                item.productType == CMTProductType.HOTEL.value &&
                        item.status == Constant.CMT_ORDER_EXIST_STATUS
            }
        }
    }

    /**
     * Get all hotel records from days before the specified dayIndex
     * @param productSelData All product selection data
     * @param dayIndex Current day index
     * @return List of hotel records from previous days
     */
    private fun getPreviousHotelsFromProductSelData(
        productSelData: List<PrdTypeRecDTO>,
        dayIndex: Int
    ): List<DailyScheduleItem> {
        return productSelData
            .take(dayIndex) // Get all records with index < dayIndex
            .flatMap { dayData ->
                dayData.dailySchedule.filter { item ->
                    item.productType == CMTProductType.HOTEL.value
                }
            }
    }

    // =================================================================================================================
    // =================================== Private Methods: Activity Recommendation Helpers ============================
    // =================================================================================================================

    /**
     * Collect all activity items that need processing from productSelData
     */
    private fun collectActivityItems(productSelData: List<PrdTypeRecDTO>): List<ActivityItem> {
        return productSelData.flatMapIndexed { _, productSel ->
            val date = productSel.date.replace("/", "-")
            productSel.dailySchedule.mapNotNull { dailyScheduleItem ->
                val productType = dailyScheduleItem.productType.lowercase()
                val status = dailyScheduleItem.status.lowercase()

                if (productType == CMTProductType.ACTIVITY.value.lowercase()
                    && status == Constant.CMT_RECOMMENDATION_STATUS
                ) {
                    ActivityItem(dailyScheduleItem.parameters, dailyScheduleItem.parameters.cityId, date)
                } else null
            }
        }
    }

    /**
     * Check if product type is activity
     */
    private fun isActivityProduct(productType: String): Boolean {
        return productType == CMTProductType.ACTIVITY.value.lowercase()
    }

    /**
     * Check if product type is transport
     */
    private fun isTransportProduct(productType: String): Boolean {
        return productType == CMTProductType.TRANSPORT.value.lowercase()
    }

    // =================================================================================================================
    // =================================== Private Methods: General Recommendation Helpers =============================
    // =================================================================================================================

    /**
     * Collect all items that need general recommendation processing from productSelData
     */
    private fun collectGeneralRecommendationItems(productSelData: List<PrdTypeRecDTO>): List<GeneralRecommendationItem> {
        return productSelData.flatMapIndexed { iDay, productSel ->
            val date = productSel.date.replace("/", "-")
            productSel.dailySchedule.mapIndexedNotNull { iProd, dailyScheduleItem ->
                val productType = dailyScheduleItem.productType.lowercase()
                val status = dailyScheduleItem.status.lowercase()

                if (status == Constant.CMT_RECOMMENDATION_STATUS) {
                    GeneralRecommendationItem(
                        iDay, iProd, productType, dailyScheduleItem.parameters,
                        date, dailyScheduleItem.parameters.cityId
                    )
                } else null
            }
        }
    }

    /**
     * Extract recommendation text and route from different data sources
     */
    private fun extractRecommendationText(
        productType: String,
        attractionData: AttractionResult?,
        cityId: Int?,
        date: String,
        recReasonData: List<RecReasonDTO>,
        iDay: Int,
        iProd: Int
    ): Pair<String, String> {
        return when {
            isActivityProduct(productType) -> {
                extractAttractionRecommendText(attractionData, cityId, date)
            }

            isTransportProduct(productType) -> {
                extractTransportRecommendText(attractionData, cityId, date)
            }

            else -> {
                extractUbtRecommendText(recReasonData, iDay, iProd, productType, date)
            }
        }
    }

    /**
     * Extract recommendation text from attraction data
     */
    private fun extractAttractionRecommendText(
        attractionData: AttractionResult?,
        cityId: Int?,
        date: String
    ): Pair<String, String> {
        val recommendText = attractionData
            ?.cities
            ?.get(cityId)
            ?.dailyData
            ?.get(date)
            ?.recommendText
            .orEmpty()

        return Pair(recommendText, Constant.CMT_RECOMMEND_TEXT_ROUTE_POI)
    }

    private fun extractTransportRecommendText(
        attractionData: AttractionResult?,
        cityId: Int?,
        date: String
    ): Pair<String, String> {
        val recommendText = attractionData
            ?.cities
            ?.get(cityId)
            ?.dailyData
            ?.get(date)
            ?.transportation
            .orEmpty()

        return Pair(recommendText, Constant.CMT_RECOMMEND_TEXT_ROUTE_POI)
    }

    /**
     * Extract recommendation text from UBT reason data
     */
    private fun extractUbtRecommendText(
        recReasonData: List<RecReasonDTO>,
        iDay: Int,
        iProd: Int,
        productType: String,
        date: String
    ): Pair<String, String> {
        val recommendText = recReasonData
            .getOrNull(iDay)
            ?.takeIf { it.date == date }
            ?.dailySchedule
            ?.firstOrNull { it.productType.lowercase() == productType.lowercase() }
            ?.recommendText
            .orEmpty()

        return Pair(recommendText, Constant.CMT_RECOMMEND_TEXT_ROUTE_UBT)
    }

    /**
     * Check if recommendation text meets length requirements and return appropriate text
     *
     * @param recommendText Original recommendation text
     * @param productType Type of product (flight, hotel, etc.)
     * @param locale User's locale
     * @param minChar Minimum character length (default: 30)
     * @return Pair of (final text, whether default was used)
     */
    private fun checkRecommendText(
        recommendText: String?,
        productType: String,
        locale: String,
    ): Pair<String, Boolean> {
        val maxChar = getLocaleMaxChar(locale)
        var useDefault = false
        var finalRecommendText = recommendText.orEmpty()

        // Check if text is too long, too short, or empty
        if (finalRecommendText.length > maxChar || finalRecommendText.length < commonConfig.minChar) {
            // Find default text for user's locale
            val config = TypedConfig.get("default-recommend-reason.yml", RecommendReasonParse())
            for ((localeKey, data) in config.current().defaultRecommendReason) {
                if (locale.take(2) == localeKey.take(2)) {
                    val recommendTextMap = data.recommendText
                    val localeProductType = covertProductType(productType)
                    val defaultText = recommendTextMap[localeProductType]
                    val randomText = defaultText?.random()
                    if (defaultText != null) {
                        finalRecommendText = randomText ?: ""
                        useDefault = true
                        break
                    }
                }
            }
        }

        return Pair(finalRecommendText, useDefault)
    }


    /**
     * Get maximum character limit based on locale
     */
    private fun getLocaleMaxChar(locale: String): Int {
        val localeChar = locale.take(2).lowercase()

        when (localeChar) {
            LOCALE_ZH -> return commonConfig.maxCharZH
            LOCALE_JA -> return commonConfig.maxCharJA
            LOCALE_KO -> return commonConfig.maxCharKO
            LOCALE_EN -> return commonConfig.maxCharEN
            LOCALE_TH -> return commonConfig.maxCharTH
        }
        return 120
    }

    /**
     * Converts a given product type string into its standardized form if it matches predefined criteria.
     *
     * @param productType the input product type string to be converted.
     * @return the standardized product type string if a match is found, otherwise null.
     */
    private fun covertProductType(productType: String): String? {
        val productTypeLower = productType.lowercase()

        listOf("flight", "train", "hotel").forEach { product ->
            if (product in productTypeLower) return product
        }

        return when {
            "airport" in productTypeLower -> "airport_transfer"
            productTypeLower == CMTProductType.RENT_CAR.value.lowercase() -> "car_rental"
            productTypeLower == CMTProductType.ACTIVITY.value.lowercase() -> "attractions"
            productTypeLower == CMTProductType.TRANSPORT.value.lowercase() -> "transport_tips"
            else -> null
        }
    }

    /**
     * Get default recommend text based on locale
     */
    private fun getDefaultRecommendText(locale: String): Map<String, List<String>> {
        return try {
            val config = TypedConfig.get("default-recommend-reason.yml", RecommendReasonParse())
            for ((localeKey, data) in config.current().defaultRecommendReason) {
                if (locale.take(2) == localeKey.take(2)) {
                    // Convert Map<String, String> to Map<String, List<String>>
                    return data.recommendText
                }
            }
            emptyMap()
        } catch (e: Exception) {
            logger.warn("Failed to get default recommend text for locale $locale: ${e.message}")
            emptyMap()
        }
    }

    // =================================================================================================================
    // =================================== Private Methods: Response Creation Helpers ==================================
    // =================================================================================================================

    /**
     * Create the final recommendation response object
     */
    private fun createRecommendResponse(productSelData: List<PrdTypeRecDTO>): List<CMTRecommendDateItemType> {
        return productSelData.mapIndexed { dayIndex, daySchedule ->
            // 1. Determine if this is the last functional day of the trip.
            val isLastDay = isEffectivelyLastDay(dayIndex, productSelData)

            // 2. Build the initial list of recommendation items for the day.
            val initialItems = buildRecommendItems(productSelData, dayIndex, daySchedule.dailySchedule, isLastDay)

            // 3. Ensure there's a hotel recommendation if needed.
            val itemsWithHotel = addDefaultHotelIfNeeded(initialItems, dayIndex, productSelData.size, productSelData)

            // 4. For all but the last day, sort items to place hotels at the end for better readability.
            val finalItems = if (isLastDay) {
                itemsWithHotel
            } else {
                sortItemsWithHotelsLast(itemsWithHotel)
            }

            // 5. Assemble the final data structure for the day.
            CMTRecommendDateItemType().apply {
                date = daySchedule.date
                recommendList = finalItems
            }
        }
    }

    /**
     * Build recommendation items from daily schedule
     */
    private fun buildRecommendItems(
        productSelData: List<PrdTypeRecDTO>,
        dayIndex: Int,
        dailySchedule: List<DailyScheduleItem>,
        isLastDay: Boolean
    ): List<CMTRecommendItemType> {
        // Get previous hotels from all days before current dayIndex
        val previousHotels = getPreviousHotelsFromProductSelData(productSelData, dayIndex)

        // Add previous hotel records for each hotel item
        dailySchedule.forEach { item ->
            if (item.productType == CMTProductType.HOTEL.value) {
                item.parameters.previousHotels = previousHotels
            }
        }

        return dailySchedule.mapIndexedNotNull { index, prod ->
            createRecommendItem(index, prod, isLastDay)
        }
    }

    /**
     * Create a single recommendation item
     */
    private fun createRecommendItem(index: Int, prod: DailyScheduleItem, isLastDay: Boolean): CMTRecommendItemType? {
        val isRecommendation = prod.status.lowercase() == Constant.CMT_RECOMMENDATION_STATUS
        val status = if (isRecommendation) 0 else 1
        return if (CMTProductType.HOTEL.value == prod.productType && (isLastDay && isRecommendation)) {
            null
        } else {
            CMTRecommendItemType().apply {
                seqNo = (index + 1).toString()
                productType = prod.productType
                desc = prod.parameters.recommendText.orEmpty()
                this.status = status // No change needed for last day hotel
                // Set product-specific properties
                configureProductSpecificProperties(this, prod.productType, prod.parameters, isRecommendation)
            }
        }
    }

    /**
     * Configure product-specific properties for recommendation item
     */
    private fun configureProductSpecificProperties(
        item: CMTRecommendItemType,
        productType: String,
        parameters: Parameter,
        isRecommendation: Boolean
    ) {
        when (productType) {
            CMTProductType.ACTIVITY.value -> {
                item.productIdList = parameters.attractionsId ?: emptyList()
            }

            CMTProductType.HOTEL.value -> {
                // Check if there are previous hotel records to determine if hotel change is needed
                val previousHotels = parameters.previousHotels ?: emptyList()
                item.isNeedChangeHotel = isRecommendation || previousHotels.isNotEmpty()

                item.productIdList = if (isRecommendation) {
                    parameters.hotelIds ?: emptyList()
                } else {
                    emptyList()
                }
                item.filters = parameters.hotelFilters?.map { hotelFilter ->
                    Filter().apply {
                        filterId = hotelFilter.filterId
                        title = hotelFilter.title
                    }
                }
            }
        }
    }

    /**
     * Add default hotel item based on check-in and check-out dates
     *
     * Logic:
     * 1. If there are existing hotel items, return as-is
     * 2. If no product data provided, use legacy logic (add hotel except on last day)
     * 3. If hotel records exist, add default hotel for dates between check-in/check-out (exclusive)
     * 4. If no hotel records, use legacy logic
     *
     * @param items Current recommendation items
     * @param dayIndex Index of the current day
     * @param totalDays Total number of days in the itinerary
     * @param productSelData Full product selection data to analyze hotel records
     * @return List of recommendation items with default hotel added if needed
     */
    private fun addDefaultHotelIfNeeded(
        items: List<CMTRecommendItemType>,
        dayIndex: Int,
        totalDays: Int,
        productSelData: List<PrdTypeRecDTO>? = null,
    ): List<CMTRecommendItemType> {
        // Early return if a hotel item already exists in the list.
        if (items.any { it.productType == CMTProductType.HOTEL.value }) {
            return items
        }

        // When productSelData is not available, we can't use the full logic.
        // The original logic correctly simplified to just checking against totalDays.
        if (productSelData == null) {
            val isLastDay = dayIndex == totalDays - 1
            return applyLegacyHotelLogic(items, isLastDay, null, emptyList())
        }

        // With productSelData available, we can reuse our helper function.
        val isLastDay = isEffectivelyLastDay(dayIndex, productSelData)

        // Get current date or return if unavailable
        val currentDate = productSelData.getOrNull(dayIndex)?.date ?: return items

        // Find hotel records with check-in/check-out dates and existing order records
        val hotelRecords = findHotelRecords(productSelData)
        val orderExistHotelRecords = findOrderExistHotelRecords(productSelData)

        // If no hotel records found, use legacy logic
        if (hotelRecords.isEmpty()) {
            return applyLegacyHotelLogic(items, isLastDay, currentDate, orderExistHotelRecords)
        }

        // Check if current date falls between any hotel's check-in and check-out dates (exclusive)
        val shouldAddDefaultHotel = hotelRecords.any { hotelRecord ->
            val checkinDate = hotelRecord.parameters.checkin
            val checkoutDate = hotelRecord.parameters.checkout

            checkinDate != null && checkoutDate != null &&
                    isDateBetweenExclusive(currentDate, checkinDate, checkoutDate)
        }

        return if (shouldAddDefaultHotel && !isLastDay) {
            items + createDefaultHotelItem(items.size, currentDate, orderExistHotelRecords)
        } else {
            items
        }
    }

    /**
     * Apply legacy hotel logic - add default hotel except on the last day
     */
    private fun applyLegacyHotelLogic(
        items: List<CMTRecommendItemType>,
        isLastDay: Boolean,
        currentDate: String?,
        orderExistHotelRecords: List<DailyScheduleItem>
    ): List<CMTRecommendItemType> {
        return if (!isLastDay) {
            items + createDefaultHotelItem(items.size, currentDate, orderExistHotelRecords)
        } else {
            items
        }
    }

    /**
     * Create a default hotel item with proper status based on hotel records
     *
     * @param currentItemCount Current number of items in the list
     * @param currentDate Current date to check (format: yyyy-MM-dd or yyyy/MM/dd)
     * @param hotelRecords List of hotel records to check against
     * @return CMTRecommendItemType with appropriate status
     */
    private fun createDefaultHotelItem(
        currentItemCount: Int,
        currentDate: String?,
        orderExistHotelRecords: List<DailyScheduleItem>
    ): CMTRecommendItemType {
        // Determine status based on hotel records and current date
        val status = if (currentDate != null && orderExistHotelRecords.isNotEmpty()) {
            // Check if current date is within any hotel's check-in and check-out range (left-closed, right-open)
            val isWithinHotelStay = orderExistHotelRecords.any { hotelRecord ->
                val checkinDate = hotelRecord.parameters.checkin
                val checkoutDate = hotelRecord.parameters.checkout

                if (checkinDate != null && checkoutDate != null) {
                    isDateBetweenLeftClosedRightOpen(currentDate, checkinDate, checkoutDate)
                } else {
                    false
                }
            }

            if (isWithinHotelStay) 1 else 0
        } else {
            // Fallback to default status when no date or hotel records available
            0
        }

        return CMTRecommendItemType().apply {
            seqNo = (currentItemCount + 1).toString()
            productType = CMTProductType.HOTEL.value
            desc = ""
            this.status = status
            isNeedChangeHotel = false
        }
    }

    /**
     * Determines if a given day is the last effective day of the itinerary.
     * The last day is either the actual last day in the list, or the day before
     * a final day that has no scheduled activities.
     */
    private fun isEffectivelyLastDay(dayIndex: Int, productSelData: List<PrdTypeRecDTO>): Boolean {
        val isActualLastDay = dayIndex == productSelData.size - 1
        if (isActualLastDay) {
            return true
        }

        // Check if the next day is the last day and has an empty schedule.
        val isDayBeforeEmptyLastDay = (dayIndex + 1 == productSelData.size - 1) &&
                (productSelData.getOrNull(dayIndex + 1)?.dailySchedule?.isEmpty() == true)

        return isDayBeforeEmptyLastDay
    }

    /**
     * Sort recommendation items to put hotel records at the end
     *
     * @param items List of recommendation items to sort
     * @return Sorted list with hotel items at the end
     */
    private fun sortItemsWithHotelsLast(items: List<CMTRecommendItemType>): List<CMTRecommendItemType> {
        val nonHotelItems = items.filter { it.productType != CMTProductType.HOTEL.value }
        val hotelItems = items.filter { it.productType == CMTProductType.HOTEL.value }

        // Combine non-hotel items first, then hotel items
        val sortedItems = nonHotelItems + hotelItems

        // Update seqNo to maintain correct sequence after sorting
        return sortedItems.mapIndexed { index, item ->
            item.apply { seqNo = (index + 1).toString() }
        }
    }

    // =================================================================================================================
    // =================================== Private Methods: Default Data Assembly Helpers ==============================
    // =================================================================================================================

    /**
     * Extract and flatten orders from the transformed request
     */
    private fun extractAndFlattenOrders(req: CMTRecReqDTO): List<Map<String, Any>> {
        val transformedRequest = transformCMTRequest(req)
        val orderList = transformedRequest["orderList"] as? List<*> ?: emptyList<Any>()

        return orderList.flatMap { order ->
            when (order) {
                is Map<*, *> -> listOf(order.asStringAnyMap())
                is List<*> -> order.filterIsInstance<Map<String, Any>>()
                else -> emptyList()
            }
        }
    }

    /**
     * Group orders by date and process them
     */
    private fun groupOrdersByDate(
        orders: List<Map<String, Any>>,
        locale: String
    ): Map<String, List<MutableMap<String, Any>>> {
        return orders
            .mapNotNull { order ->
                val date = getDate(order)
                if (date.isNotEmpty()) {
                    date to orderProcess(order, locale)
                } else {
                    null
                }
            }
            .groupBy({ it.first }, { it.second })
    }

    /**
     * Build CMT data structure from grouped orders
     */
    private fun buildCMTDataFromGroupedOrders(
        groupedOrders: Map<String, List<MutableMap<String, Any>>>
    ): List<Map<String, Any>> {
        return groupedOrders.map { (date, recommendList) ->
            val indexedRecommendList = recommendList.mapIndexed { index, item ->
                item.apply { this["seqNo"] = index + 1 }
            }

            mapOf(
                "date" to date,
                "recommendList" to indexedRecommendList
            )
        }
    }

    /**
     * Save default recommendation data to database
     */
    private fun saveDefaultRecommendationData(
        req: CMTRecReqDTO,
        requestId: String,
        mainResult: WorkflowResult,
        cmtData: List<Map<String, Any>>
    ) {
        val recommendMap = mapOf(
            Constant.CMT_RECOMMEND_DATA_KEY to cmtData,
            Constant.CMT_WORKFLOW_DATA_KEY to mainResult,
            Constant.CMT_ROUTE_INFO_KEY to RouteResp().apply {
                req.route.forEach {
                    if (Constant.CMT_DESTINATION_CITY_KEY == it.type) {
                        this.destinationCityId = it.information?.cityId
                    }
                    if (Constant.CMT_DEPARTURE_CITY_KEY == it.type) {
                        this.departureCityId = it.information?.cityId
                    }
                }
            }
        )

        val recommendRecord = UserProfileCMTRecommend(
            uid = req.uid,
            itineraryKey = req.itineraryKey,
            requestId = requestId,
            value = JsonUtil.toJson(recommendMap),
            status = Constant.CODE_SUCCESS
        )

        userProfileCMTRecommendService.saveUserProfileCMTRecommend(recommendRecord)
    }

    /**
     * Extract date from order - equivalent to Python's get_date function
     */
    private fun getDate(order: Map<String, Any>): String {
        val productType = order["productType"] as? String ?: return ""
        val information = order["information"] ?: return ""

        val dateKey = when {
            "FLIGHT" in productType || "TRAIN" in productType -> "departureTime"
            "AIRPORT" in productType -> "time"
            productType == "HOTEL" -> "checkinDate"
            productType == "RENT_CAR" -> "pickUpTime"
            productType == "ACTIVITY" -> "date"
            else -> return ""
        }

        // Use reflection to access the field from the information object
        val dateValue = try {
            val clazz = information::class.java
            val field = clazz.getDeclaredField(dateKey)
            field.isAccessible = true
            field.get(information) as? String ?: ""
        } catch (e: Exception) {
            logger.warn("Failed to extract date field '$dateKey' from information object: ${e.message}")
            ""
        }

        return if (dateValue.length >= 10) {
            dateValue.substring(0, 10)
        } else {
            dateValue
        }
    }

    /**
     * Process order - equivalent to Python's order_process function
     */
    private fun orderProcess(order: Map<String, Any>, locale: String): MutableMap<String, Any> {
        val productType = order["productType"] as? String ?: ""

        // Get default text based on locale
        val defaultText = getDefaultRecommendText(locale)

        // Initialize output
        val output = mutableMapOf<String, Any>(
            "status" to 1,
            "productType" to productType
        )

        // Add recommend text
        val productTypeLocale = covertProductType(productType)
        val listRecommendText = defaultText[productTypeLocale] ?: emptyList()
        val recommendText = if (listRecommendText.isNotEmpty()) {
            listRecommendText.random() // Random select
        } else {
            ""
        }
        output["desc"] = recommendText

        // Add product-specific fields
        if (productType in listOf("HOTEL", "ACTIVITY")) {
            output["productIdList"] = emptyList<Any>()
        }

        if (productType == "HOTEL") {
            output["needChangeHotel"] = false
        }

        return output
    }

    // =================================================================================================================
    // =================================== Private Methods: Utility & Logging ==========================================
    // =================================================================================================================

    /**
     * Check if a date is between start date and end date (left-closed, right-open: [start, end))
     *
     * @param dateToCheck Date to check (format: yyyy-MM-dd or yyyy/MM/dd)
     * @param startDate Start date (format: yyyy-MM-dd or yyyy/MM/dd)
     * @param endDate End date (format: yyyy-MM-dd or yyyy/MM/dd)
     * @return true if the date is within the range [start, end)
     */
    private fun isDateBetweenLeftClosedRightOpen(dateToCheck: String, startDate: String, endDate: String): Boolean {
        // Normalize date format, replace slashes with hyphens
        val normalizedDateToCheck = dateToCheck.replace("/", "-")
        val normalizedStartDate = startDate.replace("/", "-")
        val normalizedEndDate = endDate.replace("/", "-")

        // Compare date strings (assuming yyyy-MM-dd format, can be compared directly)
        return normalizedDateToCheck >= normalizedStartDate && normalizedDateToCheck < normalizedEndDate
    }

    /**
     * Alias for isDateBetweenLeftClosedRightOpen for backward compatibility
     */
    private fun isDateBetweenExclusive(dateToCheck: String, startDate: String, endDate: String): Boolean =
        isDateBetweenLeftClosedRightOpen(dateToCheck, startDate, endDate)

    /**
     * Extension function to safely cast Map<*, *> to Map<String, Any>
     */
    @Suppress("UNCHECKED_CAST")
    private fun Map<*, *>.asStringAnyMap(): Map<String, Any> = this as Map<String, Any>

    /**
     * Log assembling default data start
     */
    private fun logAssemblingDefaultData(uid: String, itineraryKey: String, requestId: String) {
        logger.info("Assembling default CMT recommendation data for uid=$uid, itineraryKey=$itineraryKey, requestId=$requestId")
    }

    /**
     * Log successful assembly completion
     */
    private fun logSuccessfulAssembly(uid: String, requestId: String) {
        logger.info("Successfully assembled and saved default CMT recommendation data for uid=$uid, requestId=$requestId")
    }
}