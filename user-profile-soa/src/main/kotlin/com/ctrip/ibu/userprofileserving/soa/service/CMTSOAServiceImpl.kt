package com.ctrip.ibu.userprofileserving.soa.service

import com.ctrip.framework.spring.boot.soa.SOAService
import com.ctrip.ibu.userprofileserving.application.CMTUserProfileService
import com.ctrip.ibu.userprofileserving.application.agent.cmt.AttractionAgent
import com.ctrip.ibu.userprofileserving.application.agent.cmt.HotelFilterAgent
import com.ctrip.ibu.userprofileserving.application.agent.cmt.ProductSelAgent
import com.ctrip.ibu.userprofileserving.application.agent.cmt.RecReasonAgent
import com.ctrip.ibu.userprofileserving.application.service.CDPDataService
import com.ctrip.ibu.userprofileserving.service.config.CommonConfig
import com.ctrip.ibu.userprofileserving.service.dao.entity.obkv.UserProfileCMTRecommend
import com.ctrip.ibu.userprofileserving.service.dao.entity.obkv.UserProfileTimeCost
import com.ctrip.ibu.userprofileserving.service.dto.AttractionResult
import com.ctrip.ibu.userprofileserving.service.dto.CMTRecReqDTO
import com.ctrip.ibu.userprofileserving.service.dto.Parameter
import com.ctrip.ibu.userprofileserving.service.dto.PrdTypeRecDTO
import com.ctrip.ibu.userprofileserving.service.dto.RecReasonDTO
import com.ctrip.ibu.userprofileserving.service.dto.WorkflowResult
import com.ctrip.ibu.userprofileserving.service.service.L3Service
import com.ctrip.ibu.userprofileserving.service.service.UserProfileCMTRecommendService
import com.ctrip.ibu.userprofileserving.service.service.UserProfileCostTimeService
import com.ctrip.ibu.userprofileserving.service.util.Constant
import com.ctrip.ibu.userprofileserving.service.util.DateUtils
import com.ctrip.ibu.userprofileserving.service.util.JsonUtil
import com.ctrip.ibu.userprofileserving.soa.CMTRecommendDateItemType
import com.ctrip.ibu.userprofileserving.soa.CommonRequestContext
import com.ctrip.ibu.userprofileserving.soa.GetCMTRecommendDataReqType
import com.ctrip.ibu.userprofileserving.soa.GetCMTRecommendDataRespType
import com.ctrip.ibu.userprofileserving.soa.RouteResp
import com.ctrip.ibu.userprofileserving.soa.SubmitCMTRecommendReqType
import com.ctrip.ibu.userprofileserving.soa.SubmitCMTRecommendRespType
import com.ctriposs.baiji.rpc.common.types.CheckHealthRequestType
import com.ctriposs.baiji.rpc.common.types.CheckHealthResponseType
import io.dropwizard.metrics5.Counter
import io.dropwizard.metrics5.Gauge
import io.dropwizard.metrics5.MetricName
import io.dropwizard.metrics5.MetricRegistry
import jakarta.annotation.PostConstruct
import kotlinx.coroutines.*
import kotlinx.coroutines.slf4j.MDCContext
import kotlinx.coroutines.sync.Semaphore
import kotlinx.coroutines.sync.withPermit
import org.apache.commons.lang.StringUtils
import org.slf4j.LoggerFactory
import org.slf4j.MDC
import org.springframework.util.CollectionUtils
import qunar.tc.qconfig.client.spring.QMapConfig
import java.util.UUID
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicLong
import javax.annotation.PreDestroy

/**
 * Optimised implementation of the interface that interacts with CMT.
 */
@SOAService
class CMTSOAServiceImpl(
    private val l3Service: L3Service,
    private val cmtInternalService: CMTInternalService,
    private val productSelAgent: ProductSelAgent,
    private val hotelFilterAgent: HotelFilterAgent,
    private val attractionAgent: AttractionAgent,
    private val recReasonAgent: RecReasonAgent,
    private val cdpDataService: CDPDataService,
    private val userProfileCMTRecommendService: UserProfileCMTRecommendService,
    private val userProfileCostTimeService: UserProfileCostTimeService,
    private val metricRegistry: MetricRegistry
) : CMTUserProfileService {

    companion object {
        private val logger = LoggerFactory.getLogger(CMTSOAServiceImpl::class.java)
        private const val DEFAULT_CONCURRENCY_LIMIT = 50
        private const val DEFAULT_TIMEOUT_MS = 30000L
    }

    @QMapConfig(value = "common.properties")
    lateinit var commonConfig: CommonConfig

    /** Root job for the whole service scope */
    private val serviceJob = SupervisorJob()

    /** Concurrency control - limit maximum concurrent workflows */
    private val concurrencyLimit = Semaphore(DEFAULT_CONCURRENCY_LIMIT)

    /** Global coroutine scope with IO dispatcher for better performance */
    private val serviceScope = CoroutineScope(
        serviceJob +
                Dispatchers.IO + // Changed from Default to IO for better I/O performance
                CoroutineName("CMTUserProfileService") +
                CoroutineExceptionHandler { _, e ->
                    logger.error("Top-level coroutine exception: ${e.message}", e)
                    workflowErrorCounter?.inc()
                } +
                MDCContext()
    )

    /** Track active requests (for diagnostics / cancellation) */
    private val activeRequests = ConcurrentHashMap<String, Job>()

    /** Metrics for monitoring */
    private val activeWorkflowsCount = AtomicLong(0)
    private var workflowSuccessCounter: Counter? = null
    private var workflowErrorCounter: Counter? = null
    private var workflowTimeoutCounter: Counter? = null
    private var activeWorkflowsGauge: Gauge<Long>? = null
    private var l3GenerateTimeOutCounter: Counter? = null
    private var cmtDescriptionTimeoutCounter: Counter? = null
    private var productSelTimeoutCounter: Counter? = null
    private var attractionTimeoutCounter: Counter? = null
    private var recReasonTimeoutCounter: Counter? = null
    private var hotelFilterTimeoutCounter: Counter? = null

    @PostConstruct
    fun init() {
        // Initialize metrics with tags
        metricRegistry.let { registry ->
            val tags = mutableMapOf<String, String>()
            tags["appid"] = Constant.APPID
            tags["service"] = "cmt"

            // Initialize counters
            workflowSuccessCounter = registry.counter(MetricName("cmt_workflow_success_count", tags))
            workflowErrorCounter = registry.counter(MetricName("cmt_workflow_error_count", tags))
            workflowTimeoutCounter = registry.counter(MetricName("cmt_workflow_timeout_count", tags))
            l3GenerateTimeOutCounter = registry.counter(MetricName("cmt_l3_generate_timeout_count", tags))
            cmtDescriptionTimeoutCounter = registry.counter(MetricName("cmt_description_timeout_count", tags))
            productSelTimeoutCounter = registry.counter(MetricName("cmt_product_sel_timeout_count", tags))
            attractionTimeoutCounter = registry.counter(MetricName("cmt_attraction_timeout_count", tags))
            recReasonTimeoutCounter = registry.counter(MetricName("cmt_rec_reason_timeout_count", tags))
            hotelFilterTimeoutCounter = registry.counter(MetricName("cmt_hotel_filter_timeout_count", tags))

            // Register active workflows gauge
            activeWorkflowsGauge = registry.register(
                MetricName("cmt_workflow_active_count", tags),
                Gauge { activeWorkflowsCount.get() }
            )
        }
    }

    // ────────────────── Lifecycle ──────────────────

    @PreDestroy
    fun cleanup() {
        logger.info("Shutting down CMT service, active workflows: ${activeWorkflowsCount.get()}")
        serviceJob.cancel()
        logger.info("CMT service shutdown completed")
    }

    // ────────────────── SOA Endpoints ──────────────────

    override fun checkHealth(request: CheckHealthRequestType): CheckHealthResponseType =
        CheckHealthResponseType()

    override fun getCMTRecommendData(req: GetCMTRecommendDataReqType): GetCMTRecommendDataRespType {
        return executeWithMdcContext(req.context, "getCMTRecommendData") {
            logGetRecommendDataRequest(req)
            val recommendRecord = getRecommendationRecord(req.context.uid, req.requestId)
            processRecommendationData(recommendRecord)
        }
    }

    override fun submitCMTRecommendReq(req: SubmitCMTRecommendReqType): SubmitCMTRecommendRespType {
        return executeWithMdcContext(req.context, "submitCMTRecommendReq") {
            logRequestInfo(req)
            val cmtRecReqDTO = buildCMTRecReqDTO(req)
            val existingRecords = getExistingRecommendations(req.context.uid, req.itineraryKey)
            processRecommendationRequest(cmtRecReqDTO, existingRecords)
        }
    }

    /**
     * Execute operation with MDC context management
     */
    private fun <T> executeWithMdcContext(
        context: CommonRequestContext,
        operationName: String,
        operation: () -> T
    ): T {
        addMdcInfo(context)
        return try {
            operation()
        } catch (e: Exception) {
            logger.error("$operationName failed for uid: ${context.uid}", e)
            throw e
        } finally {
            removeMdcInfo()
        }
    }

    // ────────────────── Request Processing ──────────────────

    /**
     * Get recommendation record by user ID and request ID
     */
    private fun getRecommendationRecord(uid: String, requestId: String): UserProfileCMTRecommend? {
        return userProfileCMTRecommendService.getUserProfileCMTRecommendByRequestId(uid, requestId)
    }

    /**
     * Process recommendation data and build response
     */
    private fun processRecommendationData(record: UserProfileCMTRecommend?): GetCMTRecommendDataRespType {
        return when {
            record != null && record.value != null -> {
                if (StringUtils.isBlank(record.status)) {
                    buildSuccessResponse(record.value!!)
                } else {
                    when (record.status) {
                        Constant.CODE_SUCCESS -> buildSuccessResponse(record.value!!)
                        Constant.CODE_CONTINUE -> buildContinueResponse()
                        Constant.CODE_NO_DATA -> buildNoDataResponse()
                        Constant.CODE_FAIL -> buildFailResponse()
                        else -> {
                            logger.warn("Unknown status: ${record.status}, returning continue response")
                            buildFailResponse()
                        }
                    }
                }
            }

            record != null -> {
                buildContinueResponse()
            }

            else -> {
                buildNoDataResponse()
            }
        }
    }

    /**
     * Build CMT recommendation request DTO from SOA request
     */
    private fun buildCMTRecReqDTO(req: SubmitCMTRecommendReqType): CMTRecReqDTO {
        return CMTRecReqDTO(
            uid = req.context.uid,
            locale = req.context.locale,
            context = req.context,
            head = req.head,
            itineraryKey = req.itineraryKey,
            timeInterval = req.timeInterval,
            travelers = req.travelers,
            route = req.route,
            orderList = req.orderList
        )
    }

    /**
     * Get existing recommendation records for user and itinerary
     */
    private fun getExistingRecommendations(uid: String, itineraryKey: String): List<UserProfileCMTRecommend> {
        return userProfileCMTRecommendService.getUserProfileCMTRecommend(uid, itineraryKey)
    }

    /**
     * Process recommendation request based on existing records
     */
    private fun processRecommendationRequest(
        cmtRecReqDTO: CMTRecReqDTO,
        existingRecords: List<UserProfileCMTRecommend>
    ): SubmitCMTRecommendRespType {
        val startTime = System.currentTimeMillis()
        return when {
            existingRecords.isEmpty() -> {
                return handleNewItinerary(cmtRecReqDTO, startTime)
            }

            (existingRecords.first().value == null || Constant.CODE_CONTINUE == existingRecords.first().status) -> {
                val result = SubmitCMTRecommendRespType().apply {
                    resultCode = Constant.CODE_SUCCESS
                    resultMsg = Constant.CODE_SUCCESS_MSG
                    requestId = existingRecords.first().requestId
                }
                result
            }

            shouldSkipL3Update(cmtRecReqDTO.uid) -> {
                val result = handleExistingItineraryNoUpdate(cmtRecReqDTO, existingRecords.first())
                result
            }

            else -> {
                val result = handleExistingItineraryWithUpdate(startTime,cmtRecReqDTO, existingRecords.first())
                result
            }
        }
    }

    /**
     * Check if L3 update should be skipped
     */
    private fun shouldSkipL3Update(uid: String): Boolean {
        val l3Uid = getEffectiveL3Uid(uid)
        return !l3Service.isNeedUpdateL3Blocking(
            l3Uid,
            DateUtils.now(),
            Constant.L3_TOPIC,
            Constant.L3_TOPIC_ID
        )
    }

    // ────────────────── Itinerary Handlers ──────────────────

    /**
     * Handle existing itinerary without L3 update
     */
    private fun handleExistingItineraryNoUpdate(
        req: CMTRecReqDTO,
        record: UserProfileCMTRecommend
    ): SubmitCMTRecommendRespType {
        logger.info("Existing itinerary - no L3 update needed, requestId: ${record.requestId}")
        val existRequestId = record.requestId

        // get existing recommendation result
        val recommendResult = userProfileCMTRecommendService.getUserProfileCMTRecommend(
            req.uid,
            req.itineraryKey
        )

        // Get existing main result
        val mainResult = getExistingWorkflowResult(req.uid, req.itineraryKey, existRequestId, recommendResult)

        // Get existing recommendation items
        val recItemTypes = getExistingRecommendItems(req.uid, req.itineraryKey, existRequestId, recommendResult)

        // Execute assembleCMTRecommendData synchronously
        if (mainResult.productSelData.isNotEmpty()) {
            userProfileCMTRecommendService.saveUserProfileCMTRecommend(
                UserProfileCMTRecommend(
                    uid = req.uid,
                    itineraryKey = req.itineraryKey,
                    requestId = existRequestId,
                    value = record.value,
                    status = Constant.CODE_CONTINUE
                )
            )
            serviceScope.launch {
                cmtInternalService.refreshHotelProducts(record.requestId, req, mainResult, recItemTypes)
            }
        }

        return SubmitCMTRecommendRespType().apply {
            resultCode = Constant.CODE_SUCCESS
            resultMsg = Constant.CODE_SUCCESS_MSG
            requestId = existRequestId
        }
    }

    private fun handleNewItinerary(req: CMTRecReqDTO, startTime: Long): SubmitCMTRecommendRespType {
        val requestId = UUID.randomUUID().toString()
        val uid = req.uid

        logger.info("New itinerary – uid={}, itineraryKey={}, requestId={}", uid, req.itineraryKey, requestId)

        userProfileCMTRecommendService.saveUserProfileCMTRecommend(
            UserProfileCMTRecommend(uid, req.itineraryKey, requestId, null, Constant.CODE_CONTINUE)
        )

        launchWorkflow(req, requestId, false, startTime)

        return SubmitCMTRecommendRespType().apply {
            resultCode = Constant.CODE_SUCCESS
            resultMsg = Constant.CODE_SUCCESS_MSG
            this.requestId = requestId
        }
    }

    private fun handleExistingItineraryWithUpdate(
        startTime: Long,
        req: CMTRecReqDTO,
        record: UserProfileCMTRecommend
    ): SubmitCMTRecommendRespType {
        val requestId = record.requestId
        logger.info(
            "Existing itinerary – refresh L3, uid={}, itineraryKey={}, requestId={}",
            req.uid,
            record.itineraryKey,
            requestId
        )

        userProfileCMTRecommendService.saveUserProfileCMTRecommend(
            UserProfileCMTRecommend(
                uid = req.uid,
                itineraryKey = req.itineraryKey,
                requestId = requestId,
                value = record.value,
                status = Constant.CODE_CONTINUE
            )
        )

        launchWorkflow(req, requestId, refreshOnlyL3 = true, startTime)

        return SubmitCMTRecommendRespType().apply {
            resultCode = Constant.CODE_SUCCESS
            resultMsg = Constant.CODE_SUCCESS_MSG
            this.requestId = requestId
        }
    }

    // ────────────────── Workflow orchestration ──────────────────

    private fun launchWorkflow(
        req: CMTRecReqDTO,
        requestId: String,
        refreshOnlyL3: Boolean = false,
        startTime: Long
    ) {
        val job = serviceScope.launch(MDCContext()) {
            concurrencyLimit.withPermit {
                activeWorkflowsCount.incrementAndGet()
                try {
                    if (refreshOnlyL3) {
                        refreshL3Only(req, requestId)
                    } else {
                        processAsyncWorkflow(req, requestId)
                    }
                    workflowSuccessCounter?.inc()
                } catch (ce: CancellationException) {
                    logger.warn("Workflow cancelled – requestId={}", requestId)
                    throw ce
                } catch (e: Exception) {
                    logger.error("Workflow failed – requestId=${requestId}: {}", e.message, e)
                    workflowErrorCounter?.inc()
                } finally {
                    activeWorkflowsCount.decrementAndGet()
                    recordProcessingCostTime(
                        req.uid,
                        requestId,
                        if (refreshOnlyL3) Constant.CMT_REC_ONLY_L3_COST_TYPE else Constant.CMT_REC_TOTAL_COST_TYPE,
                        startTime
                    )
                }
            }
        }

        activeRequests[requestId] = job
        job.invokeOnCompletion { activeRequests.remove(requestId) }
    }

    /** Refresh only L3 and hotel filter */
    private suspend fun refreshL3Only(req: CMTRecReqDTO, requestId: String) = coroutineScope {
        logger.info("Start async L3 refresh – uid={}, itineraryKey={}, requestId={}", req.uid, req.itineraryKey, requestId)

        // Execute L3 generation in parallel with unified timeout handling
        val l3Deferred = async {
            executeWithTimeoutOrNull(
                commonConfig.l3AgentTimeoutMs,
                { l3Service.generateL3(getEffectiveL3Uid(req.uid), DateUtils.now(), Constant.L3_TOPIC, Constant.L3_TOPIC_ID, requestId) },
                "L3 generation",
                requestId
            )
        }

        // get exist recommendation result
        val recommendResult = userProfileCMTRecommendService.getUserProfileCMTRecommend(req.uid, req.itineraryKey)

        // Get existing recommendation results
        val mainResult = getExistingWorkflowResult(req.uid, req.itineraryKey, requestId,recommendResult)

        // Get existing recommendation data
        val recItemTypes = getExistingRecommendItems(req.uid,req.itineraryKey,requestId,recommendResult)

        // Wait for L3 result, use fallback data on timeout
        val l3Result = l3Deferred.await() ?: getDefaultL3Data(req, requestId)

        // Execute hotel filtering in parallel with unified timeout handling
        val hotelFilterDeferred = async {
            executeWithTimeoutOrNull(
                commonConfig.hotelFilterAgentTimeoutMs,
                { hotelFilterAgent.getHotelFilterNoPOIData(
                    l3Result,
                    mainResult.productSelData,
                    requestId,
                    req
                ) },
                "Hotel filter",
                requestId
            )
        }

        // Wait for hotel filter result, use fallback data on timeout
        val hotelFilterData = hotelFilterDeferred.await() ?: getDefaultHotelFilterData(req, requestId)

        mainResult.hotelFilterData = hotelFilterData

        if (mainResult.productSelData.isNotEmpty()) {
            cmtInternalService.refreshHotelProducts(requestId, req, mainResult, recItemTypes)
        }

        logger.info(
            "Async L3 refresh finished – uid={}, itineraryKey={}, requestId={}",
            req.uid,
            req.itineraryKey,
            requestId
        )
    }

    /** Full workflow with timeout & fallback */
    private suspend fun processAsyncWorkflow(req: CMTRecReqDTO, requestId: String) = supervisorScope {
        val uid = req.uid
        logger.info("Start full workflow – uid={}, itineraryKey={}, requestId={}", uid, req.itineraryKey, requestId)

        val mainResult = executeWithTimeout(
            commonConfig.workflowAllTimeoutMs,
            { executeMainWorkflow(req, requestId) },
            { getDefaultWorkflowResult() },
            Constant.WORKFLOW_OPERATION_FULL,
            requestId
        )

        if (mainResult.productSelData.isEmpty()) {
            cmtInternalService.assembleCMTDefaultData(requestId, req, mainResult)
        } else {
            cmtInternalService.assembleCMTRecommendData(requestId, req, mainResult)
        }

        logger.info(
            "Full workflow success – uid={}, itineraryKey={}, requestId={}",
            uid,
            req.itineraryKey,
            requestId
        )
    }

    /** Core business steps – all‑for‑one failure semantics */
    private suspend fun executeMainWorkflow(req: CMTRecReqDTO, requestId: String): WorkflowResult =
        coroutineScope {
            val uid = req.uid

            // Phase 1: Execute L3 and description service in parallel
            val l3Deferred = async {
                executeWithTimeoutOrNull(
                    commonConfig.l3AgentTimeoutMs,
                    { l3Service.generateL3(getEffectiveL3Uid(uid), DateUtils.now(), Constant.L3_TOPIC, Constant.L3_TOPIC_ID, requestId) },
                    Constant.WORKFLOW_OPERATION_L3_GENERATE,
                    requestId
                )
            }

            val descDeferred = async {
                executeWithTimeoutOrNull(
                    commonConfig.cmtDescriptionAgentTimeoutMs,
                    { cmtInternalService.getCMTDescription(req, requestId) },
                    Constant.WORKFLOW_OPERATION_CMT_DESCRIPTION,
                    requestId
                )
            }

            // Wait for description result for subsequent dependencies
            val description = descDeferred.await() ?: getDefaultDescription(req, requestId)
            if(StringUtils.isBlank(description)){
                logger.warn("CMT description is blank, returning default workflow result for requestId: $requestId")
                return@coroutineScope getDefaultWorkflowResult()
            }

            // Phase 2: Execute product selection and attraction recommendation in parallel based on the description
            val productSelDeferred = async {
                executeWithTimeoutOrNull(
                    commonConfig.productSelAgentTimeoutMs,
//                    { productSelAgent.getProductSelData(description, requestId, uid) },
                    { productSelAgent.getProductSelDataByRequest(req, requestId, description) },
                    Constant.WORKFLOW_OPERATION_PRODUCT_SELECT,
                    requestId
                )
            }

            val attractionDeferred = async {
                executeWithTimeoutOrNull(
                    commonConfig.attractionAgentTimeoutMs,
                    { attractionAgent.getAttractionData(req, description, requestId) },
                    Constant.WORKFLOW_OPERATION_ATTRACTION,
                    requestId
                )
            }

            // Wait for L3 and product selection results
            val l3Result = l3Deferred.await() ?: getDefaultL3Data(req, requestId)
            val productSelData = productSelDeferred.await()

            // If product selection fails or times out, return the default result directly
            if (productSelData == null || CollectionUtils.isEmpty(productSelData)) {
                productSelTimeoutCounter?.inc()
                logger.warn("Product selection failed or timeout, returning default workflow result for requestId: $requestId")
                return@coroutineScope getDefaultWorkflowResult()
            }

            // Phase 3: Execute recommendation reason and hotel filtering based on previous results
            val recReasonDeferred = async {
                executeWithTimeoutOrNull(
                    commonConfig.reasonAgentTimeoutMs,
                    { recReasonAgent.getRecReasonData(req, productSelData, requestId) },
                    Constant.WORKFLOW_OPERATION_REC_REASON,
                    requestId
                )
            }

            val noPOIHotelFilterDeferred = async {
                executeWithTimeoutOrNull(
                    commonConfig.hotelFilterAgentTimeoutMs,
                    { hotelFilterAgent.getHotelFilterNoPOIData(l3Result, productSelData, requestId, req) },
                    Constant.WORKFLOW_OPERATION_HOTEL_FILTER,
                    requestId
                )
            }

            // Wait for all results
            val attractionData = attractionDeferred.await() ?: getDefaultAttractionData(req, requestId)
            val recReasonData = recReasonDeferred.await() ?: getDefaultRecReasonData(req, requestId)
            val noPOIHotelFilterData = noPOIHotelFilterDeferred.await() ?: getDefaultHotelFilterData(req, requestId)

            WorkflowResult(
                productSelData = productSelData,
                hotelFilterData = noPOIHotelFilterData,
                attractionData = attractionData,
                recReasonData = recReasonData
            )
        }

    /**
     * Get effective L3 UID, using mockL3Uid if available and not blank, otherwise fallback to provided uid
     */
    private fun getEffectiveL3Uid(uid: String): String {
        return commonConfig.mockL3Uid?.takeIf { it.isNotBlank() } ?: uid
    }

    // ────────────────── Fallback Data Providers ──────────────────

    /**
     * Generic fallback data provider with logging
     */
    private fun <T> getDefaultData(
        dataType: String,
        req: CMTRecReqDTO,
        defaultValue: T,
        requestId: String
    ): T {
        logDefaultDataUsage(dataType, req.uid, req.itineraryKey, requestId)
        return defaultValue
    }

    private fun getDefaultL3Data(req: CMTRecReqDTO, requestId: String): String {
        logDefaultDataUsage("L3 data from CDP", req.uid, req.itineraryKey, requestId)
        l3GenerateTimeOutCounter?.inc()
        return cdpDataService.getCDPData(req.uid, requestId)
    }

    private fun getDefaultDescription(req: CMTRecReqDTO, requestId: String): String {
        cmtDescriptionTimeoutCounter?.inc()
        return getDefaultData("description", req, StringUtils.EMPTY, requestId)
    }

    private fun getDefaultRecReasonData(req: CMTRecReqDTO, requestId: String): List<RecReasonDTO> {
        recReasonTimeoutCounter?.inc()
        return getDefaultData("recommendation reason data", req, emptyList(), requestId)
    }

    private fun getDefaultHotelFilterData(req: CMTRecReqDTO, requestId: String): Map<Int, Parameter> {
        hotelFilterTimeoutCounter?.inc()
        return getDefaultData("hotel filter data", req, emptyMap(), requestId)
    }

    private fun getDefaultAttractionData(req: CMTRecReqDTO, requestId: String): AttractionResult {
        attractionTimeoutCounter?.inc()
        return getDefaultData("attraction data", req, AttractionResult(
            cities = emptyMap()
        ), requestId)
    }

    /**
     * Log default data usage
     */
    private fun logDefaultDataUsage(dataType: String, uid: String, itineraryKey: String, requestId: String) {
        logger.info("Using default $dataType for uid: $uid, itineraryKey: $itineraryKey,requestId: $requestId")
    }


    // ────────────────── Response Building ──────────────────

    /**
     * Build success response with recommendation data using Jackson
     */
    private fun buildSuccessResponse(jsonData: String): GetCMTRecommendDataRespType {
        return try {
            logger.debug("Parsing recommendation JSON data with Jackson")

            // Parse the main JSON object to extract the recommendation data
            val jsonMap = JsonUtil.fromJson<Map<String, Any>>(jsonData)
            if (jsonMap == null) {
                logger.warn("Failed to parse main JSON object, falling back to continue response")
                return buildNoDataResponse()
            }

            // Extract the recommendation data - it could be a List or a JSON string
            val recommendData = jsonMap[Constant.CMT_RECOMMEND_DATA_KEY]
            val recommendationData = when (recommendData) {
                is List<*> -> {
                    // If it's already a List, convert it to the target type
                    try {
                        val jsonString = JsonUtil.toJson(recommendData)
                        JsonUtil.fromJsonArray(jsonString, CMTRecommendDateItemType::class.java)
                    } catch (e: Exception) {
                        logger.error("Failed to convert List to CMTRecommendDateItemType array: ${e.message}")
                        null
                    }
                }
                is String -> {
                    // If it's a JSON string, parse it directly
                    if (recommendData.isNotBlank()) {
                        JsonUtil.fromJsonArray(recommendData, CMTRecommendDateItemType::class.java)
                    } else {
                        null
                    }
                }
                else -> {
                    logger.warn("Recommendation data is neither List nor String, type: ${recommendData?.javaClass?.simpleName}")
                    null
                }
            }
            GetCMTRecommendDataRespType().apply {
                resultCode = Constant.CODE_SUCCESS
                resultMsg = Constant.CODE_SUCCESS_MSG
                data = recommendationData
                route = jsonMap[Constant.CMT_ROUTE_INFO_KEY]?.let { routeInfo ->
                    JsonUtil.fromJson(JsonUtil.toJson(routeInfo), RouteResp::class.java)
                }
            }
        } catch (e: Exception) {
            logger.error("Failed to parse recommendation JSON data with Jackson: ${e.message}", e)
            buildFailResponse()
        }
    }

    /**
     * Build continue response when no data is available
     */
    private fun buildContinueResponse(): GetCMTRecommendDataRespType {
        logger.info("No recommendation data available, returning continue response")

        return GetCMTRecommendDataRespType().apply {
            resultCode = Constant.CODE_CONTINUE
            resultMsg = Constant.CODE_CONTINUE_MSG
            data = null
        }
    }


    private fun buildNoDataResponse(): GetCMTRecommendDataRespType {
        logger.info("No recommendation data available, returning no data response")

        return GetCMTRecommendDataRespType().apply {
            resultCode = Constant.CODE_NO_DATA
            resultMsg = Constant.CODE_NO_DATA_MSG
            data = null
        }
    }

    private fun buildFailResponse(): GetCMTRecommendDataRespType {
        logger.info("Failed to process recommendation request, returning fail response")

        return GetCMTRecommendDataRespType().apply {
            resultCode = Constant.CODE_FAIL
            resultMsg = Constant.CODE_FAIL_MSG
            data = null
        }
    }

    // ────────────────── Unified Timeout Handling ──────────────────

    /**
     * Execute operation with timeout and comprehensive error handling
     * @param timeoutMs Timeout in milliseconds
     * @param operation The operation to execute
     * @param operationName Name for logging
     * @param requestId Request ID for logging
     * @param onTimeout Callback for timeout handling (returns fallback value)
     * @param onError Callback for error handling (returns fallback value)
     * @return Result of operation or fallback value
     */
    private suspend fun <T> executeWithTimeoutAndHandling(
        timeoutMs: Long,
        operation: suspend () -> T,
        operationName: String,
        requestId: String,
        onTimeout: () -> T,
        onError: (Exception) -> T
    ): T {
        return try {
            val effectiveTimeout = if (timeoutMs <= 0) {
                logInvalidTimeout(timeoutMs, operationName)
                DEFAULT_TIMEOUT_MS
            } else {
                timeoutMs
            }

            withTimeout(effectiveTimeout) { operation() }
        } catch (e: TimeoutCancellationException) {
            logTimeout(operationName, timeoutMs, requestId)
            workflowTimeoutCounter?.inc()
            onTimeout()
        } catch (e: CancellationException) {
            logCancellation(operationName, requestId)
            throw e // Re-throw cancellation
        } catch (e: Exception) {
            logError(operationName, requestId, e)
            onError(e)
        }
    }

    /**
     * Execute operation with timeout and fallback handling
     * @param timeoutMs Timeout in milliseconds
     * @param operation The operation to execute
     * @param fallback Fallback function to call on timeout/error
     * @param operationName Name for logging
     * @param requestId Request ID for logging
     * @return Result of operation or fallback
     */
    private suspend fun <T> executeWithTimeout(
        timeoutMs: Long,
        operation: suspend () -> T,
        fallback: () -> T,
        operationName: String,
        requestId: String
    ): T {
        return executeWithTimeoutAndHandling(
            timeoutMs = timeoutMs,
            operation = operation,
            operationName = operationName,
            requestId = requestId,
            onTimeout = fallback,
            onError = { fallback() }
        )
    }

    /**
     * Execute operation with timeout returning nullable result
     * @param timeoutMs Timeout in milliseconds
     * @param operation The operation to execute
     * @param operationName Name for logging
     * @param requestId Request ID for logging
     * @return Result of operation or null on timeout/error
     */
    private suspend fun <T> executeWithTimeoutOrNull(
        timeoutMs: Long,
        operation: suspend () -> T,
        operationName: String,
        requestId: String
    ): T? {
        return executeWithTimeoutAndHandling(
            timeoutMs = timeoutMs,
            operation = operation,
            operationName = operationName,
            requestId = requestId,
            onTimeout = { null },
            onError = { null }
        )
    }

    // ────────────────── Logging Utilities ──────────────────

    /**
     * Log invalid timeout warning
     */
    private fun logInvalidTimeout(timeoutMs: Long, operationName: String) {
        logger.warn("Invalid timeout ${timeoutMs}ms for $operationName, using default ${DEFAULT_TIMEOUT_MS}ms")
    }

    /**
     * Log timeout warning
     */
    private fun logTimeout(operationName: String, timeoutMs: Long, requestId: String) {
        logger.warn("$operationName timeout (${timeoutMs}ms) for requestId: $requestId")
    }

    /**
     * Log cancellation debug message
     */
    private fun logCancellation(operationName: String, requestId: String) {
        logger.debug("$operationName cancelled for requestId: $requestId")
    }

    /**
     * Log error message
     */
    private fun logError(operationName: String, requestId: String, exception: Exception) {
        logger.error("$operationName failed for requestId: $requestId", exception)
    }

    // ────────────────── JSON Processing helpers ──────────────────


    private fun getExistingRecommendItems(
        uid: String,
        itineraryKey: String,
        requestId: String,
        recommendResult: List<UserProfileCMTRecommend>
    ): List<CMTRecommendDateItemType> {
        return try {
            logger.info("Getting existing recommendation items for uid: $uid, itineraryKey: $itineraryKey, requestId: $requestId")

            if (recommendResult.isEmpty()) {
                logger.warn("No existing recommendation found for uid: $uid, itineraryKey: $itineraryKey, requestId: $requestId")
                return emptyList()
            }

            val recommendData = recommendResult.first().value
            if (recommendData.isNullOrBlank()) {
                logger.warn("Empty recommendation data for uid: $uid, itineraryKey: $itineraryKey,requestId: $requestId")
                return emptyList()
            }

            parseRecommendItemsFromJson(recommendData, requestId)

        } catch (e: Exception) {
            logger.error("Failed to get existing recommendation items for requestId $requestId: ${e.message}", e)
            emptyList()
        }
    }

    /**
     * Parse recommendation items from JSON string with comprehensive error handling using Jackson
     * @param jsonData JSON string containing recommendation data
     * @param requestId Request ID for logging
     * @return List of CMTRecommendDateItemType objects or empty list if parsing fails
     */
    private fun parseRecommendItemsFromJson(jsonData: String, requestId: String): List<CMTRecommendDateItemType> {
        return try {
            logger.info("Parsing recommendation items from JSON using Jackson for requestId: $requestId")

            // Parse the JSON data as a Map to extract the recommendation data
            val jsonMap = JsonUtil.fromJson(jsonData, Map::class.java) as Map<String, Any>

            // Extract the recommendation data using CMT_RECOMMEND_DATA_KEY
            val recommendData = jsonMap[Constant.CMT_RECOMMEND_DATA_KEY]
            val recommendationData = when (recommendData) {
                is List<*> -> {
                    // If it's already a List, convert it to the target type
                    try {
                        val jsonString = JsonUtil.toJson(recommendData)
                        JsonUtil.fromJsonArray(jsonString, CMTRecommendDateItemType::class.java)
                    } catch (e: Exception) {
                        logger.error("Failed to convert List to CMTRecommendDateItemType array for requestId $requestId: ${e.message}")
                        null
                    }
                }
                is String -> {
                    // If it's a JSON string, parse it directly
                    if (recommendData.isNotBlank()) {
                        try {
                            JsonUtil.fromJsonArray(recommendData, CMTRecommendDateItemType::class.java)
                        } catch (e: Exception) {
                            logger.error("Failed to parse JSON string to CMTRecommendDateItemType array for requestId $requestId: ${e.message}")
                            null
                        }
                    } else {
                        logger.warn("Recommendation data string is blank for requestId $requestId")
                        null
                    }
                }
                else -> {
                    logger.warn("Recommendation data is neither List nor String, type: ${recommendData?.javaClass?.simpleName} for requestId $requestId")
                    null
                }
            }

            if (recommendationData == null) {
                logger.error("Failed to parse recommendation items from recommendation data for requestId $requestId")
                return emptyList()
            }

            logger.info("Successfully parsed ${recommendationData.size} recommendation items using Jackson for requestId: $requestId")
            recommendationData

        } catch (e: Exception) {
            logger.error("Failed to parse recommendation items from JSON using Jackson for requestId $requestId: ${e.message}", e)
            logger.debug("JSON data that failed to parse: $jsonData")
            emptyList()
        }
    }

    /**
     * Get existing workflow result from stored recommendation data
     * @param uid User ID
     * @param itineraryKey Itinerary key
     * @param requestId Request ID for logging
     * @return WorkflowResult object or default if parsing fails
     */
    private fun getExistingWorkflowResult(uid: String, itineraryKey: String, requestId: String,
                                          recommendResult: List<UserProfileCMTRecommend>): WorkflowResult {
        return try {
            logger.info("Getting existing workflow result for uid: $uid, itineraryKey: $itineraryKey , requestId: $requestId")

            if (recommendResult.isEmpty()) {
                logger.warn("No existing recommendation found for uid: $uid, itineraryKey: $itineraryKey, requestId: $requestId")
                return getDefaultWorkflowResult()
            }

            val recommendData = recommendResult.first().value
            if (recommendData.isNullOrBlank()) {
                logger.warn("Empty recommendation data for uid: $uid, itineraryKey: $itineraryKey , requestId: $requestId")
                return getDefaultWorkflowResult()
            }

            parseWorkflowResultFromJson(recommendData, requestId)

        } catch (e: Exception) {
            logger.error("Failed to get existing workflow result for requestId $requestId: ${e.message}", e)
            getDefaultWorkflowResult()
        }
    }

    /**
     * Parse WorkflowResult from JSON string with comprehensive error handling using Jackson
     * @param jsonData JSON string containing workflow data
     * @param requestId Request ID for logging
     * @return WorkflowResult object or default if parsing fails
     */
    private fun parseWorkflowResultFromJson(jsonData: String, requestId: String): WorkflowResult {
        return try {
            logger.info("Parsing workflow result from JSON using Jackson for requestId: $requestId")

            // First level: Parse outer JSON to get workflow data
            val outerJsonMap = JsonUtil.fromJson<Map<String, Any>>(jsonData)
            if (outerJsonMap == null) {
                logger.error("Failed to parse outer JSON for requestId $requestId")
                logger.debug("Outer JSON content: $jsonData")
                throw IllegalStateException("Invalid outer JSON format")
            }

            // Extract workflow data - it could be either a JSON object or a JSON string
            val workflowDataValue = outerJsonMap[Constant.CMT_WORKFLOW_DATA_KEY]
            if (workflowDataValue == null) {
                logger.error("Failed to extract workflow data from JSON for requestId $requestId")
                throw IllegalStateException("Missing workflow data key")
            }

            val workflowResult = when (workflowDataValue) {
                // Case 1: workflow data is already a Map (JSON object) - convert to WorkflowResult
                is Map<*, *> -> {
                    logger.debug("Workflow data is a JSON object, converting to WorkflowResult for requestId: $requestId")
                    parseWorkflowResultFromMap(workflowDataValue, requestId)
                }
                // Case 2: workflow data is a JSON string - parse it
                is String -> {
                    if (workflowDataValue.isBlank()) {
                        logger.error("Workflow data string is blank for requestId $requestId")
                        null
                    } else {
                        logger.debug("Workflow data is a JSON string, parsing to WorkflowResult for requestId: $requestId")
                        JsonUtil.fromJson(workflowDataValue, WorkflowResult::class.java)
                    }
                }
                else -> {
                    logger.error("Unexpected workflow data type: ${workflowDataValue::class.simpleName} for requestId $requestId")
                    null
                }
            }

            if (workflowResult == null) {
                logger.error("Failed to parse WorkflowResult from workflow data for requestId $requestId")
                throw IllegalStateException("Invalid WorkflowResult format")
            }

            logger.info("Successfully parsed workflow result using Jackson for requestId: $requestId")
            workflowResult

        } catch (e: Exception) {
            logger.error("Failed to parse workflow result from JSON using Jackson for requestId $requestId: ${e.message}", e)
            getDefaultWorkflowResult()
        }
    }

    /**
     * Parse WorkflowResult from Map object with special handling for complex types
     * @param workflowDataMap Map containing workflow data
     * @param requestId Request ID for logging
     * @return WorkflowResult object or null if parsing fails
     */
    private fun parseWorkflowResultFromMap(workflowDataMap: Map<*, *>, requestId: String): WorkflowResult? {
        return try {
            logger.debug("Parsing WorkflowResult from Map for requestId: $requestId")

            // Extract basic fields
            val productSelData = parseProductSelData(workflowDataMap["productSelData"], requestId)
            val attractionData = parseAttractionData(workflowDataMap["attractionData"], requestId)
            val recReasonData = parseRecReasonData(workflowDataMap["recReasonData"], requestId)
            val hotelFilterData = parseHotelFilterData(workflowDataMap["hotelFilterData"], requestId)

            WorkflowResult(
                productSelData = productSelData,
                hotelFilterData = hotelFilterData,
                attractionData = attractionData,
                recReasonData = recReasonData
            )
        } catch (e: Exception) {
            logger.error("Failed to parse WorkflowResult from Map for requestId $requestId: ${e.message}", e)
            null
        }
    }

    /**
     * Generic parser with error handling and logging
     */
    private fun <T> parseDataWithErrorHandling(
        data: Any?,
        requestId: String,
        dataType: String,
        defaultValue: T,
        parser: (Any) -> T?
    ): T {
        return try {
            when (data) {
                null -> {
                    logParsingInfo("$dataType is null", requestId)
                    defaultValue
                }
                else -> {
                    parser(data) ?: run {
                        logUnexpectedDataType(dataType, requestId, data)
                        defaultValue
                    }
                }
            }
        } catch (e: Exception) {
            logParsingError(dataType, requestId, e)
            defaultValue
        }
    }

    /**
     * Parse productSelData from raw data
     */
    private fun parseProductSelData(data: Any?, requestId: String): List<PrdTypeRecDTO> {
        return parseDataWithErrorHandling(
            data = data,
            requestId = requestId,
            dataType = "productSelData",
            defaultValue = emptyList()
        ) { rawData ->
            when (rawData) {
                is List<*> -> {
                    val jsonString = JsonUtil.toJson(rawData)
                    JsonUtil.fromJsonArray(jsonString, PrdTypeRecDTO::class.java)
                }
                else -> null
            }
        }
    }

    /**
     * Parse attractionData from raw data
     */
    private fun parseAttractionData(data: Any?, requestId: String): AttractionResult? {
        return parseDataWithErrorHandling(
            data = data,
            requestId = requestId,
            dataType = "attractionData",
            defaultValue = null
        ) { rawData ->
            when (rawData) {
                is Map<*, *> -> {
                    val jsonString = JsonUtil.toJson(rawData)
                    JsonUtil.fromJson(jsonString, AttractionResult::class.java)
                }
                else -> null
            }
        }
    }

    /**
     * Parse recReasonData from raw data
     */
    private fun parseRecReasonData(data: Any?, requestId: String): List<RecReasonDTO> {
        return parseDataWithErrorHandling(
            data = data,
            requestId = requestId,
            dataType = "recReasonData",
            defaultValue = emptyList()
        ) { rawData ->
            when (rawData) {
                is List<*> -> {
                    val jsonString = JsonUtil.toJson(rawData)
                    JsonUtil.fromJsonArray(jsonString, RecReasonDTO::class.java)
                }
                else -> null
            }
        }
    }

    // ────────────────── Parsing Logging Utilities ──────────────────

    /**
     * Log parsing information
     */
    private fun logParsingInfo(message: String, requestId: String) {
        logger.debug("$message for requestId: $requestId")
    }

    /**
     * Log unexpected data type warning
     */
    private fun logUnexpectedDataType(dataType: String, requestId: String, data: Any) {
        logger.warn("Unexpected $dataType type for requestId $requestId: ${data.javaClass.simpleName}")
    }

    /**
     * Log parsing error
     */
    private fun logParsingError(dataType: String, requestId: String, exception: Exception) {
        logger.error("Failed to parse $dataType for requestId $requestId: ${exception.message}", exception)
    }

    /**
     * Parse hotelFilterData from raw data with simple Int keys
     */
    private fun parseHotelFilterData(data: Any?, requestId: String): Map<Int, Parameter> {
        return parseDataWithErrorHandling(
            data = data,
            requestId = requestId,
            dataType = "hotelFilterData",
            defaultValue = emptyMap()
        ) { rawData ->
            when (rawData) {
                is Map<*, *> -> parseHotelFilterMap(rawData, requestId)
                else -> null
            }
        }
    }

    /**
     * Parse hotel filter map with proper key and value handling
     */
    private fun parseHotelFilterMap(dataMap: Map<*, *>, requestId: String): Map<Int, Parameter> {
        return dataMap.mapNotNull { (key, value) ->
            parseHotelFilterEntry(key, value, requestId)
        }.toMap()
    }

    /**
     * Parse a single hotel filter entry
     */
    private fun parseHotelFilterEntry(key: Any?, value: Any?, requestId: String): Pair<Int, Parameter>? {
        return try {
            val dayIndex = parseHotelFilterKey(key, requestId) ?: return null
            val parameter = parseHotelFilterValue(value, requestId) ?: return null
            Pair(dayIndex, parameter)
        } catch (e: Exception) {
            logHotelFilterEntryError(key, value, requestId, e)
            null
        }
    }

    /**
     * Parse hotel filter key into Int (day index)
     */
    private fun parseHotelFilterKey(key: Any?, requestId: String): Int? {
        return when (key) {
            is String -> parseStringKeyToInt(key, requestId)
            is Number -> key.toInt()
            is List<*> -> parseListKeyToInt(key, requestId)
            else -> {
                logUnsupportedKeyType(key, requestId)
                null
            }
        }
    }

    /**
     * Parse string key to Int (day index)
     */
    private fun parseStringKeyToInt(key: String, requestId: String): Int? {
        return try {
            key.trim().toInt()
        } catch (e: NumberFormatException) {
            logger.warn("Failed to parse string key '$key' to Int for requestId $requestId: ${e.message}")
            null
        }
    }

    /**
     * Parse list key to Int (take first element as day index)
     */
    private fun parseListKeyToInt(key: List<*>, requestId: String): Int? {
        return try {
            if (key.isNotEmpty()) {
                (key[0] as Number).toInt()
            } else {
                logger.warn("Empty list key for requestId $requestId")
                null
            }
        } catch (e: Exception) {
            logger.warn("Failed to parse list key $key to Int for requestId $requestId: ${e.message}")
            null
        }
    }

    /**
     * Parse string key format like "(1,2)" or "1,2" - Legacy method for backward compatibility
     */
    private fun parseStringKey(key: String, requestId: String): Pair<Int, Int>? {
        return try {
            val cleanKey = key.trim('(', ')', ' ')
            val parts = cleanKey.split(",")

            if (parts.size == 2) {
                val first = parts[0].trim().toInt()
                val second = parts[1].trim().toInt()
                Pair(first, second)
            } else {
                logInvalidKeyFormat(key, requestId)
                null
            }
        } catch (e: NumberFormatException) {
            logInvalidKeyFormat(key, requestId, e)
            null
        }
    }

    /**
     * Parse list key format [1, 2] - Legacy method for backward compatibility
     */
    private fun parseListKey(key: List<*>, requestId: String): Pair<Int, Int>? {
        return try {
            if (key.size == 2) {
                val first = (key[0] as Number).toInt()
                val second = (key[1] as Number).toInt()
                Pair(first, second)
            } else {
                logInvalidListKeyFormat(key, requestId)
                null
            }
        } catch (e: Exception) {
            logInvalidListKeyFormat(key, requestId, e)
            null
        }
    }

    /**
     * Parse hotel filter value into Parameter
     */
    private fun parseHotelFilterValue(value: Any?, requestId: String): Parameter? {
        return value?.let {
            try {
                val parameterJson = JsonUtil.toJson(it)
                JsonUtil.fromJson(parameterJson, Parameter::class.java)
            } catch (e: Exception) {
                logValueParsingError(value, requestId, e)
                null
            }
        }
    }

    // ────────────────── Hotel Filter Logging Utilities ──────────────────

    /**
     * Log hotel filter entry parsing error
     */
    private fun logHotelFilterEntryError(key: Any?, value: Any?, requestId: String, exception: Exception) {
        logger.warn("Failed to parse hotel filter entry key=$key, value=$value for requestId $requestId: ${exception.message}")
    }

    /**
     * Log unsupported key type
     */
    private fun logUnsupportedKeyType(key: Any?, requestId: String) {
        logger.warn("Unsupported key type: ${key?.javaClass?.simpleName} for requestId $requestId")
    }

    /**
     * Log invalid key format
     */
    private fun logInvalidKeyFormat(key: String, requestId: String, exception: Exception? = null) {
        val message = "Invalid key format: $key for requestId $requestId"
        if (exception != null) {
            logger.warn("$message: ${exception.message}")
        } else {
            logger.warn(message)
        }
    }

    /**
     * Log invalid list key format
     */
    private fun logInvalidListKeyFormat(key: List<*>, requestId: String, exception: Exception? = null) {
        val message = "Invalid list key format: $key for requestId $requestId"
        if (exception != null) {
            logger.warn("$message: ${exception.message}")
        } else {
            logger.warn(message)
        }
    }

    /**
     * Log value parsing error
     */
    private fun logValueParsingError(value: Any?, requestId: String, exception: Exception) {
        logger.warn("Failed to parse parameter value: $value for requestId $requestId: ${exception.message}")
    }

    /**
     * Get default WorkflowResult when parsing fails
     */
    private fun getDefaultWorkflowResult(): WorkflowResult {
        logger.info("Using default workflow result")
        return WorkflowResult(
            productSelData = emptyList(),
            hotelFilterData = emptyMap(),
            attractionData = null,
            recReasonData = emptyList()
        )
    }

    // ────────────────── Performance Monitoring ──────────────────

    /**
     * Record processing cost time for different recommendation request types
     */
    private fun recordProcessingCostTime(
        uid: String,
        requestId: String,
        processType: String,
        startTime: Long,
    ) {
        try {
            val costTime = System.currentTimeMillis() - startTime

            userProfileCostTimeService.saveCostTime(
                UserProfileTimeCost(
                    uid = uid,
                    requestId = requestId,
                    costType = processType,
                    costTime = costTime,
                    input = null,
                    output = null
                )
            )

            logger.info("CMT processing ($processType) for uid: $uid, requestId: $requestId completed in ${costTime}ms")
        } catch (e: Exception) {
            logger.error("Failed to save CMT processing cost time for uid: $uid, requestId: $requestId, processType: $processType: ${e.message}", e)
        }
    }

    // ────────────────── Logging Helpers ──────────────────

    /**
     * Log incoming get recommendation data request
     */
    private fun logGetRecommendDataRequest(req: GetCMTRecommendDataReqType) {
        logger.info(
            "Get CMT recommend data - uid: ${req.context.uid}, " +
            "requestId: ${req.requestId}, " +
            "traceId: ${req.context.traceId}"
        )
    }

    /**
     * Log incoming request information
     */
    private fun logRequestInfo(req: SubmitCMTRecommendReqType) {
        logger.info(
            "CMT submit recommend req - uid: ${req.context.uid}, " +
            "itineraryKey: ${req.itineraryKey}, " +
            "traceId: ${req.context.traceId}, " +
            "request: ${JsonUtil.toJson(req)}"
        )
    }

    // ────────────────── MDC Helpers ──────────────────

    private fun addMdcInfo(context: CommonRequestContext?) {
        MDC.put("trace_id", context?.traceId)
        MDC.put("uid", context?.uid)
    }

    private fun removeMdcInfo() {
        MDC.clear()
    }

}