<?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <appender name="Console" class="ch.qos.logback.core.ConsoleAppender">
    <encoder>
      <pattern>%date %level [%thread] %10logger [%file:%line] %msg%n</pattern>
    </encoder>
  </appender>
  <appender name="TripLog" class="com.ctrip.framework.triplog.client.appender.TripLogbackAppender">
  </appender>

<!--  <appender name="CentralLogging" class="com.ctrip.framework.clogging.agent.appender.CLoggingTagAppender">-->
<!--  </appender>-->

<!--  <appender name="Clog" class="com.ctrip.framework.clogging.agent.appender.CLoggingLineNumberAwareAppender"/>-->

  <logger name="OBKV-RUNTIME" level="OFF" additivity="false" />

  <root level="INFO">
    <appender-ref ref="TripLog"/>
    <appender-ref ref="Console"/>
  </root>
</configuration>